{"expo": {"name": "make_me", "slug": "make_me", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/images/icon.png", "scheme": "makeme", "userInterfaceStyle": "automatic", "newArchEnabled": true, "ios": {"supportsTablet": true}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/images/adaptive-icon.png", "backgroundColor": "#ffffff"}, "edgeToEdgeEnabled": true, "permissions": ["android.permission.CAMERA", "android.permission.RECORD_AUDIO", "android.permission.READ_EXTERNAL_STORAGE", "android.permission.WRITE_EXTERNAL_STORAGE", "android.permission.MODIFY_AUDIO_SETTINGS", "android.permission.CAMERA", "android.permission.RECORD_AUDIO", "android.permission.READ_EXTERNAL_STORAGE", "android.permission.WRITE_EXTERNAL_STORAGE", "android.permission.MODIFY_AUDIO_SETTINGS"], "package": "com.interplay_djexo.make_me"}, "web": {"bundler": "metro", "output": "static", "favicon": "./assets/images/favicon.png"}, "plugins": ["expo-router", ["expo-splash-screen", {"image": "./assets/images/splash-icon.png", "imageWidth": 200, "resizeMode": "contain", "backgroundColor": "#ffffff"}], "expo-camera", "expo-media-library", "expo-av"], "experiments": {"typedRoutes": true}}}