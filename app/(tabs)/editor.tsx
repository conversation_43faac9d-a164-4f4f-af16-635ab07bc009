import * as Haptics from 'expo-haptics';
import { LinearGradient } from 'expo-linear-gradient';
import { router } from 'expo-router';
import React from 'react';
import { Dimensions, ScrollView, TouchableOpacity, View } from 'react-native';

import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { VideoCard } from '@/components/ui/Card';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { Colors, Gradients } from '@/constants/Colors';
import { useColorScheme } from '@/hooks/useColorScheme';

const { width } = Dimensions.get('window');

const editorActions = [
  {
    id: 'capture',
    title: 'Record',
    description: 'Capture new video',
    icon: 'video.fill',
    color: '#EF4444',
    route: '/editor/capture',
  },
  {
    id: 'gallery',
    title: 'Import',
    description: 'From gallery',
    icon: 'photo.on.rectangle',
    color: '#3B82F6',
    route: '/editor/gallery',
  },
  {
    id: 'projects',
    title: 'Projects',
    description: 'My videos',
    icon: 'folder.fill',
    color: '#8B5CF6',
    route: '/editor/projects',
  },
  {
    id: 'storyboard',
    title: 'Storyboard',
    description: 'AI editing',
    icon: 'rectangle.3.group',
    color: '#10B981',
    route: '/editor/storyboard',
  },
];

const aiFeatures = [
  {
    title: 'Smart Auto-Edit',
    description: 'AI analyzes your footage and creates professional edits automatically',
    icon: 'wand.and.stars',
    color: '#8B5CF6',
  },
  {
    title: 'Highlight Detection',
    description: 'Find the most engaging moments in your videos using ML',
    icon: 'sparkles',
    color: '#F59E0B',
  },
  {
    title: 'Beat Sync',
    description: 'Automatically sync your clips to music beats',
    icon: 'music.note',
    color: '#EF4444',
  },
  {
    title: 'Auto Captions',
    description: 'Generate accurate subtitles with speech recognition',
    icon: 'captions.bubble',
    color: '#10B981',
  },
];

export default function EditorScreen() {
  const colorScheme = useColorScheme();
  const colors = Colors[colorScheme ?? 'light'];

  const handleActionPress = (route: string) => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    router.push(route as any);
  };

  return (
    <ThemedView style={styles.container}>
      {/* Header */}
      <LinearGradient
        colors={Gradients[colorScheme ?? 'light'].video}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
        style={styles.header}
      >
        <View style={styles.headerContent}>
          <ThemedText type="title" style={styles.headerTitle}>
            Video Editor
          </ThemedText>
          <ThemedText type="body" style={styles.headerSubtitle}>
            Professional video editing powered by AI
          </ThemedText>
        </View>
      </LinearGradient>

      <ScrollView
        style={styles.content}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.scrollContent}
      >
        {/* Quick Actions */}
        <View style={styles.section}>
          <ThemedText type="heading" style={styles.sectionTitle}>
            Get Started
          </ThemedText>
          <View style={styles.actionsGrid}>
            {editorActions.map((action) => (
              <TouchableOpacity
                key={action.id}
                style={styles.actionCard}
                onPress={() => handleActionPress(action.route)}
                activeOpacity={0.8}
              >
                <VideoCard style={styles.actionContent}>
                  <View style={[styles.actionIcon, { backgroundColor: action.color }]}>
                    <IconSymbol name={action.icon} size={28} color="#FFFFFF" />
                  </View>
                  <ThemedText type="label" style={styles.actionTitle}>
                    {action.title}
                  </ThemedText>
                  <ThemedText type="caption" style={styles.actionDescription}>
                    {action.description}
                  </ThemedText>
                </VideoCard>
              </TouchableOpacity>
            ))}
          </View>
        </View>

        {/* AI Features */}
        <View style={styles.section}>
          <ThemedText type="heading" style={styles.sectionTitle}>
            AI-Powered Tools
          </ThemedText>
          <View style={styles.featuresContainer}>
            {aiFeatures.map((feature, index) => (
              <VideoCard key={index} style={styles.featureCard}>
                <View style={styles.featureContent}>
                  <View style={[styles.featureIconContainer, { backgroundColor: feature.color }]}>
                    <IconSymbol
                      name={feature.icon}
                      size={24}
                      color="#FFFFFF"
                    />
                  </View>
                  <View style={styles.featureTextContainer}>
                    <ThemedText type="subheading" style={styles.featureTitle}>
                      {feature.title}
                    </ThemedText>
                    <ThemedText type="caption" style={styles.featureDescription}>
                      {feature.description}
                    </ThemedText>
                  </View>
                </View>
              </VideoCard>
            ))}
          </View>
        </View>
      </ScrollView>
    </ThemedView>
  );
}

const styles = {
  container: {
    flex: 1,
  },
  header: {
    paddingTop: 60,
    paddingBottom: 30,
    paddingHorizontal: 20,
  },
  headerContent: {
    alignItems: 'center',
  },
  headerTitle: {
    color: '#FFFFFF',
    textAlign: 'center',
    marginBottom: 8,
  },
  headerSubtitle: {
    color: 'rgba(255, 255, 255, 0.9)',
    textAlign: 'center',
  },
  content: {
    flex: 1,
  },
  scrollContent: {
    paddingBottom: 30,
  },
  section: {
    paddingHorizontal: 20,
    marginBottom: 32,
  },
  sectionTitle: {
    marginBottom: 16,
  },
  actionsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    gap: 12,
  },
  actionCard: {
    width: (width - 52) / 2,
  },
  actionContent: {
    alignItems: 'center',
    padding: 20,
  },
  actionIcon: {
    width: 56,
    height: 56,
    borderRadius: 28,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 12,
  },
  actionTitle: {
    textAlign: 'center',
    marginBottom: 4,
  },
  actionDescription: {
    textAlign: 'center',
  },
  featuresContainer: {
    gap: 12,
  },
  featureCard: {
    width: '100%',
  },
  featureContent: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
  },
  featureIconContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 16,
  },
  featureTextContainer: {
    flex: 1,
  },
  featureTitle: {
    marginBottom: 4,
  },
  featureDescription: {
    opacity: 0.8,
  },
};
