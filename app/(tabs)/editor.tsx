import React from 'react';
import { ScrollView, StyleSheet, View } from 'react-native';

import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { useColorScheme } from '@/hooks/useColorScheme';



export default function EditorScreen() {
  const colorScheme = useColorScheme();

  return (
    <ThemedView style={styles.container}>
      <View style={styles.header}>
        <ThemedText type="title">Video Storyteller</ThemedText>
        <ThemedText>Create amazing videos with AI</ThemedText>
      </View>

      <ScrollView style={styles.content} contentContainerStyle={styles.contentContainer}>
        <View style={styles.featuresContainer}>
          <ThemedText type="subtitle">Features</ThemedText>
          <View style={styles.featuresList}>
            <FeatureItem title="Auto-Edit" description="One-tap smart editing" />
            <FeatureItem title="Highlight Detection" description="Find the best moments" />
            <FeatureItem title="Beat Sync" description="Music matched to your clips" />
            <FeatureItem title="Auto Captions" description="Speech-to-text subtitles" />
          </View>
        </View>
      </ScrollView>
    </ThemedView>
  );
}

function FeatureItem({ title, description }: { title: string; description: string }) {
  return (
    <View style={styles.featureItem}>
      <View>
        <ThemedText type="defaultSemiBold">{title}</ThemedText>
        <ThemedText style={styles.featureDescription}>{description}</ThemedText>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    paddingTop: 60,
    paddingHorizontal: 20,
    paddingBottom: 20,
  },
  content: {
    flex: 1,
  },
  contentContainer: {
    paddingHorizontal: 20,
    paddingBottom: 40,
  },
  featuresContainer: {
    marginTop: 30,
  },
  featuresList: {
    marginTop: 16,
    gap: 16,
  },
  featureItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 16,
  },
  featureDescription: {
    fontSize: 14,
    opacity: 0.7,
  },
});
