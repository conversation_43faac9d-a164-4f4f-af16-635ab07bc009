import * as Haptics from 'expo-haptics';
import { LinearGradient } from 'expo-linear-gradient';
import React from 'react';
import { Dimensions, ScrollView, TouchableOpacity, View } from 'react-native';

import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { VideoCard } from '@/components/ui/Card';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { Colors, Gradients } from '@/constants/Colors';
import { useColorScheme } from '@/hooks/useColorScheme';

const { width } = Dimensions.get('window');

const tutorials = [
  {
    id: 1,
    title: 'Getting Started with AI Editing',
    description: 'Learn the basics of AI-powered video editing',
    duration: '5 min',
    difficulty: 'Beginner',
    icon: 'play.circle.fill',
    color: '#10B981',
  },
  {
    id: 2,
    title: 'Advanced Filter Techniques',
    description: 'Master professional video filters and effects',
    duration: '8 min',
    difficulty: 'Intermediate',
    icon: 'camera.filters',
    color: '#8B5CF6',
  },
  {
    id: 3,
    title: 'Auto-Caption Setup',
    description: 'Configure speech recognition for subtitles',
    duration: '3 min',
    difficulty: 'Beginner',
    icon: 'captions.bubble.fill',
    color: '#F59E0B',
  },
  {
    id: 4,
    title: 'Music Sync & Beat Detection',
    description: 'Sync your videos to music automatically',
    duration: '10 min',
    difficulty: 'Advanced',
    icon: 'music.note',
    color: '#EF4444',
  },
];

const inspirationCategories = [
  {
    title: 'Travel Videos',
    count: 24,
    icon: 'airplane',
    color: '#3B82F6',
  },
  {
    title: 'Food & Cooking',
    count: 18,
    icon: 'fork.knife',
    color: '#F59E0B',
  },
  {
    title: 'Fitness & Sports',
    count: 32,
    icon: 'figure.run',
    color: '#10B981',
  },
  {
    title: 'Art & Creative',
    count: 15,
    icon: 'paintbrush.fill',
    color: '#EC4899',
  },
];

const tips = [
  {
    title: 'Use Natural Lighting',
    description: 'Film during golden hour for the best results',
    icon: 'sun.max.fill',
  },
  {
    title: 'Stable Shots',
    description: 'Keep your phone steady or use a tripod',
    icon: 'camera.fill',
  },
  {
    title: 'Plan Your Story',
    description: 'Think about the beginning, middle, and end',
    icon: 'list.bullet.clipboard',
  },
];

export default function ExploreScreen() {
  const colorScheme = useColorScheme();
  const colors = Colors[colorScheme ?? 'light'];

  const handleTutorialPress = (tutorial: any) => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    // In a real app, this would open the tutorial video
  };

  const handleCategoryPress = (category: any) => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    // In a real app, this would show videos in that category
  };

  return (
    <ThemedView style={styles.container}>
      {/* Header */}
      <LinearGradient
        colors={Gradients[colorScheme ?? 'light'].secondary}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
        style={styles.header}
      >
        <View style={styles.headerContent}>
          <ThemedText type="title" style={styles.headerTitle}>
            Explore & Learn
          </ThemedText>
          <ThemedText type="body" style={styles.headerSubtitle}>
            Discover tutorials, tips, and inspiration
          </ThemedText>
        </View>
      </LinearGradient>

      <ScrollView
        style={styles.content}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.scrollContent}
      >
        {/* Tutorials Section */}
        <View style={styles.section}>
          <ThemedText type="heading" style={styles.sectionTitle}>
            Video Tutorials
          </ThemedText>
          <View style={styles.tutorialsContainer}>
            {tutorials.map((tutorial) => (
              <TouchableOpacity
                key={tutorial.id}
                onPress={() => handleTutorialPress(tutorial)}
                activeOpacity={0.8}
              >
                <VideoCard style={styles.tutorialCard}>
                  <View style={styles.tutorialContent}>
                    <View style={[styles.tutorialIcon, { backgroundColor: tutorial.color }]}>
                      <IconSymbol name={tutorial.icon} size={24} color="#FFFFFF" />
                    </View>
                    <View style={styles.tutorialInfo}>
                      <ThemedText type="subheading" style={styles.tutorialTitle}>
                        {tutorial.title}
                      </ThemedText>
                      <ThemedText type="caption" style={styles.tutorialDescription}>
                        {tutorial.description}
                      </ThemedText>
                      <View style={styles.tutorialMeta}>
                        <ThemedText type="caption" style={styles.tutorialDuration}>
                          {tutorial.duration}
                        </ThemedText>
                        <ThemedText type="caption" style={styles.tutorialDifficulty}>
                          {tutorial.difficulty}
                        </ThemedText>
                      </View>
                    </View>
                  </View>
                </VideoCard>
              </TouchableOpacity>
            ))}
          </View>
        </View>

        {/* Inspiration Categories */}
        <View style={styles.section}>
          <ThemedText type="heading" style={styles.sectionTitle}>
            Video Inspiration
          </ThemedText>
          <View style={styles.categoriesGrid}>
            {inspirationCategories.map((category, index) => (
              <TouchableOpacity
                key={index}
                style={styles.categoryCard}
                onPress={() => handleCategoryPress(category)}
                activeOpacity={0.8}
              >
                <VideoCard style={styles.categoryContent}>
                  <View style={[styles.categoryIcon, { backgroundColor: category.color }]}>
                    <IconSymbol name={category.icon} size={20} color="#FFFFFF" />
                  </View>
                  <ThemedText type="label" style={styles.categoryTitle}>
                    {category.title}
                  </ThemedText>
                  <ThemedText type="caption" style={styles.categoryCount}>
                    {category.count} videos
                  </ThemedText>
                </VideoCard>
              </TouchableOpacity>
            ))}
          </View>
        </View>

        {/* Pro Tips */}
        <View style={styles.section}>
          <ThemedText type="heading" style={styles.sectionTitle}>
            Pro Tips
          </ThemedText>
          <View style={styles.tipsContainer}>
            {tips.map((tip, index) => (
              <VideoCard key={index} style={styles.tipCard}>
                <View style={styles.tipContent}>
                  <IconSymbol
                    name={tip.icon}
                    size={24}
                    color={colors.primary}
                    style={styles.tipIcon}
                  />
                  <View style={styles.tipTextContainer}>
                    <ThemedText type="subheading" style={styles.tipTitle}>
                      {tip.title}
                    </ThemedText>
                    <ThemedText type="caption" style={styles.tipDescription}>
                      {tip.description}
                    </ThemedText>
                  </View>
                </View>
              </VideoCard>
            ))}
          </View>
        </View>
      </ScrollView>
    </ThemedView>
  );
}

const styles = {
  container: {
    flex: 1,
  },
  header: {
    paddingTop: 60,
    paddingBottom: 30,
    paddingHorizontal: 20,
  },
  headerContent: {
    alignItems: 'center',
  },
  headerTitle: {
    color: '#FFFFFF',
    textAlign: 'center',
    marginBottom: 8,
  },
  headerSubtitle: {
    color: 'rgba(255, 255, 255, 0.9)',
    textAlign: 'center',
  },
  content: {
    flex: 1,
  },
  scrollContent: {
    paddingBottom: 30,
  },
  section: {
    paddingHorizontal: 20,
    marginBottom: 32,
  },
  sectionTitle: {
    marginBottom: 16,
  },
  tutorialsContainer: {
    gap: 12,
  },
  tutorialCard: {
    width: '100%',
  },
  tutorialContent: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
  },
  tutorialIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 16,
  },
  tutorialInfo: {
    flex: 1,
  },
  tutorialTitle: {
    marginBottom: 4,
  },
  tutorialDescription: {
    marginBottom: 8,
    opacity: 0.8,
  },
  tutorialMeta: {
    flexDirection: 'row',
    gap: 12,
  },
  tutorialDuration: {
    opacity: 0.6,
  },
  tutorialDifficulty: {
    opacity: 0.6,
  },
  categoriesGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    gap: 12,
  },
  categoryCard: {
    width: (width - 52) / 2,
  },
  categoryContent: {
    alignItems: 'center',
    padding: 16,
  },
  categoryIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 12,
  },
  categoryTitle: {
    textAlign: 'center',
    marginBottom: 4,
  },
  categoryCount: {
    textAlign: 'center',
    opacity: 0.7,
  },
  tipsContainer: {
    gap: 12,
  },
  tipCard: {
    width: '100%',
  },
  tipContent: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
  },
  tipIcon: {
    marginRight: 16,
  },
  tipTextContainer: {
    flex: 1,
  },
  tipTitle: {
    marginBottom: 4,
  },
  tipDescription: {
    opacity: 0.8,
  },
};
