import * as Haptics from 'expo-haptics';
import { LinearGradient } from 'expo-linear-gradient';
import { router } from 'expo-router';
import React from 'react';
import { Dimensions, ScrollView, TouchableOpacity, View } from 'react-native';

import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { VideoCard } from '@/components/ui/Card';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { Colors, Gradients } from '@/constants/Colors';
import { useColorScheme } from '@/hooks/useColorScheme';

const { width } = Dimensions.get('window');

const quickActions = [
  {
    id: 'capture',
    title: 'Record Video',
    description: 'Capture new footage',
    icon: 'video.fill',
    color: '#EF4444',
    route: '/editor/capture',
  },
  {
    id: 'gallery',
    title: 'Import Media',
    description: 'Choose from gallery',
    icon: 'photo.on.rectangle',
    color: '#3B82F6',
    route: '/editor/gallery',
  },
  {
    id: 'projects',
    title: 'My Projects',
    description: 'Continue editing',
    icon: 'folder.fill',
    color: '#8B5CF6',
    route: '/editor/projects',
  },
];

const features = [
  {
    title: 'AI Auto-Edit',
    description: 'Smart editing with one tap',
    icon: 'wand.and.stars',
  },
  {
    title: 'Highlight Detection',
    description: 'Find the best moments automatically',
    icon: 'sparkles',
  },
  {
    title: 'Beat Sync',
    description: 'Music matched to your clips',
    icon: 'music.note',
  },
  {
    title: 'Auto Captions',
    description: 'Speech-to-text subtitles',
    icon: 'captions.bubble',
  },
];

export default function HomeScreen() {
  const colorScheme = useColorScheme();
  const colors = Colors[colorScheme ?? 'light'];

  const handleQuickAction = (route: string) => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    router.push(route as any);
  };

  return (
    <ThemedView style={styles.container}>
      {/* Header with Gradient */}
      <LinearGradient
        colors={Gradients[colorScheme ?? 'light'].primary}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
        style={styles.header}
      >
        <View style={styles.headerContent}>
          <ThemedText type="title" style={styles.headerTitle}>
            AI Video Studio
          </ThemedText>
          <ThemedText type="body" style={styles.headerSubtitle}>
            Create stunning videos with artificial intelligence
          </ThemedText>
        </View>
      </LinearGradient>

      <ScrollView
        style={styles.content}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.scrollContent}
      >
        {/* Quick Actions */}
        <View style={styles.section}>
          <ThemedText type="heading" style={styles.sectionTitle}>
            Quick Actions
          </ThemedText>
          <View style={styles.quickActionsGrid}>
            {quickActions.map((action) => (
              <TouchableOpacity
                key={action.id}
                style={styles.quickActionCard}
                onPress={() => handleQuickAction(action.route)}
                activeOpacity={0.8}
              >
                <VideoCard style={styles.quickActionContent}>
                  <View style={[styles.quickActionIcon, { backgroundColor: action.color }]}>
                    <IconSymbol name={action.icon} size={24} color="#FFFFFF" />
                  </View>
                  <ThemedText type="label" style={styles.quickActionTitle}>
                    {action.title}
                  </ThemedText>
                  <ThemedText type="caption" style={styles.quickActionDescription}>
                    {action.description}
                  </ThemedText>
                </VideoCard>
              </TouchableOpacity>
            ))}
          </View>
        </View>

        {/* Features Section */}
        <View style={styles.section}>
          <ThemedText type="heading" style={styles.sectionTitle}>
            AI-Powered Features
          </ThemedText>
          <View style={styles.featuresGrid}>
            {features.map((feature, index) => (
              <VideoCard key={index} style={styles.featureCard}>
                <View style={styles.featureContent}>
                  <IconSymbol
                    name={feature.icon}
                    size={32}
                    color={colors.primary}
                    style={styles.featureIcon}
                  />
                  <ThemedText type="subheading" style={styles.featureTitle}>
                    {feature.title}
                  </ThemedText>
                  <ThemedText type="caption" style={styles.featureDescription}>
                    {feature.description}
                  </ThemedText>
                </View>
              </VideoCard>
            ))}
          </View>
        </View>
      </ScrollView>
    </ThemedView>
  );
}

const styles = {
  container: {
    flex: 1,
  },
  header: {
    paddingTop: 60,
    paddingBottom: 30,
    paddingHorizontal: 20,
  },
  headerContent: {
    alignItems: 'center',
  },
  headerTitle: {
    color: '#FFFFFF',
    textAlign: 'center',
    marginBottom: 8,
  },
  headerSubtitle: {
    color: 'rgba(255, 255, 255, 0.9)',
    textAlign: 'center',
  },
  content: {
    flex: 1,
  },
  scrollContent: {
    paddingBottom: 30,
  },
  section: {
    paddingHorizontal: 20,
    marginBottom: 32,
  },
  sectionTitle: {
    marginBottom: 16,
  },
  quickActionsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    gap: 12,
  },
  quickActionCard: {
    width: (width - 52) / 3,
  },
  quickActionContent: {
    alignItems: 'center',
    padding: 16,
  },
  quickActionIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 12,
  },
  quickActionTitle: {
    textAlign: 'center',
    marginBottom: 4,
  },
  quickActionDescription: {
    textAlign: 'center',
  },
  featuresGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    gap: 12,
  },
  featureCard: {
    width: (width - 52) / 2,
  },
  featureContent: {
    alignItems: 'center',
    padding: 20,
  },
  featureIcon: {
    marginBottom: 12,
  },
  featureTitle: {
    textAlign: 'center',
    marginBottom: 8,
  },
  featureDescription: {
    textAlign: 'center',
  },
};
