import React, { useState, useEffect } from 'react';
import { StyleSheet, View, TouchableOpacity, Alert, ActivityIndicator, Share } from 'react-native';
import { router, useLocalSearchParams } from 'expo-router';
import { Video, ResizeMode } from 'expo-av';
import * as MediaLibrary from 'expo-media-library';
import * as FileSystem from 'expo-file-system';
import * as Sharing from 'expo-sharing';

import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { Colors } from '@/constants/Colors';
import { useColorScheme } from '@/hooks/useColorScheme';

type ExportQuality = {
  id: string;
  name: string;
  resolution: string;
  bitrate: string;
};

const exportQualities: ExportQuality[] = [
  { id: 'low', name: 'Low', resolution: '480p', bitrate: '1 Mbps' },
  { id: 'medium', name: 'Medium', resolution: '720p', bitrate: '3 Mbps' },
  { id: 'high', name: 'High', resolution: '1080p', bitrate: '8 Mbps' },
];

export default function ExportScreen() {
  const params = useLocalSearchParams();
  const colorScheme = useColorScheme();
  const [videoUri, setVideoUri] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);
  const [exporting, setExporting] = useState(false);
  const [exportProgress, setExportProgress] = useState(0);
  const [exportedUri, setExportedUri] = useState<string | null>(null);
  const [selectedQuality, setSelectedQuality] = useState('medium');

  useEffect(() => {
    if (params.uri) {
      setVideoUri(params.uri as string);
    } else {
      Alert.alert('Error', 'No video provided');
      router.back();
    }
  }, [params.uri]);

  const handleExport = async () => {
    if (!videoUri) return;
    
    setExporting(true);
    setExportProgress(0);
    
    // Simulate export process with progress updates
    const progressInterval = setInterval(() => {
      setExportProgress(prev => {
        const newProgress = prev + Math.random() * 10;
        return newProgress > 100 ? 100 : newProgress;
      });
    }, 300);
    
    try {
      // In a real app, this would use FFmpeg to process the video
      // For now, we'll just simulate a delay and use the original video
      await new Promise(resolve => setTimeout(resolve, 3000));
      
      // Save to media library
      const asset = await MediaLibrary.createAssetAsync(videoUri);
      const albumName = 'Video Storyteller';
      
      // Create album if it doesn't exist
      const albums = await MediaLibrary.getAlbumsAsync();
      let album = albums.find(a => a.title === albumName);
      
      if (!album) {
        album = await MediaLibrary.createAlbumAsync(albumName, asset, false);
      } else {
        await MediaLibrary.addAssetsToAlbumAsync([asset], album, false);
      }
      
      setExportedUri(videoUri);
      clearInterval(progressInterval);
      setExportProgress(100);
    } catch (error) {
      console.error('Export error:', error);
      Alert.alert('Export Failed', 'There was an error exporting your video');
      clearInterval(progressInterval);
    } finally {
      setExporting(false);
    }
  };

  const handleShare = async () => {
    if (!exportedUri) return;
    
    try {
      if (await Sharing.isAvailableAsync()) {
        await Sharing.shareAsync(exportedUri, {
          mimeType: 'video/mp4',
          dialogTitle: 'Share your video',
        });
      } else {
        Alert.alert('Sharing not available', 'Sharing is not available on this device');
      }
    } catch (error) {
      console.error('Sharing error:', error);
      Alert.alert('Sharing Failed', 'There was an error sharing your video');
    }
  };

  const handleQualitySelect = (qualityId: string) => {
    setSelectedQuality(qualityId);
  };

  if (loading) {
    return (
      <ThemedView style={styles.container}>
        <ActivityIndicator size="large" color={Colors[colorScheme ?? 'light'].tint} />
      </ThemedView>
    );
  }

  return (
    <ThemedView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity onPress={() => router.back()}>
          <IconSymbol 
            name="chevron.left" 
            size={24} 
            color={Colors[colorScheme ?? 'light'].text} 
          />
        </TouchableOpacity>
        <ThemedText type="subtitle">Export Video</ThemedText>
        <View style={{ width: 24 }} />
      </View>

      {videoUri && (
        <View style={styles.videoContainer}>
          <Video
            style={styles.video}
            source={{ uri: videoUri }}
            resizeMode={ResizeMode.CONTAIN}
            useNativeControls
          />
        </View>
      )}

      {!exportedUri ? (
        <>
          <View style={styles.qualityContainer}>
            <ThemedText type="subtitle">Export Quality</ThemedText>
            <View style={styles.qualityOptions}>
              {exportQualities.map((quality) => (
                <TouchableOpacity
                  key={quality.id}
                  style={[
                    styles.qualityOption,
                    selectedQuality === quality.id && styles.selectedQualityOption,
                  ]}
                  onPress={() => handleQualitySelect(quality.id)}
                >
                  <ThemedText 
                    style={[
                      styles.qualityName,
                      selectedQuality === quality.id && styles.selectedQualityText,
                    ]}
                  >
                    {quality.name}
                  </ThemedText>
                  <ThemedText 
                    style={[
                      styles.qualityDetails,
                      selectedQuality === quality.id && styles.selectedQualityText,
                    ]}
                  >
                    {quality.resolution} • {quality.bitrate}
                  </ThemedText>
                </TouchableOpacity>
              ))}
            </View>
          </View>

          <View style={styles.exportButtonContainer}>
            {exporting ? (
              <View style={styles.exportingContainer}>
                <ActivityIndicator color={Colors[colorScheme ?? 'light'].tint} />
                <ThemedText style={styles.exportingText}>
                  Exporting... {Math.round(exportProgress)}%
                </ThemedText>
              </View>
            ) : (
              <TouchableOpacity
                style={styles.exportButton}
                onPress={handleExport}
              >
                <IconSymbol name="square.and.arrow.down" size={20} color="#FFFFFF" />
                <ThemedText style={styles.exportButtonText}>Export Video</ThemedText>
              </TouchableOpacity>
            )}
          </View>
        </>
      ) : (
        <View style={styles.successContainer}>
          <View style={styles.successIconContainer}>
            <IconSymbol name="checkmark.circle.fill" size={60} color={Colors[colorScheme ?? 'light'].tint} />
          </View>
          <ThemedText type="subtitle">Export Complete!</ThemedText>
          <ThemedText style={styles.successText}>
            Your video has been saved to your gallery
          </ThemedText>
          
          <View style={styles.actionButtonsContainer}>
            <TouchableOpacity
              style={styles.actionButton}
              onPress={handleShare}
            >
              <IconSymbol name="square.and.arrow.up" size={20} color="#FFFFFF" />
              <ThemedText style={styles.actionButtonText}>Share</ThemedText>
            </TouchableOpacity>
            
            <TouchableOpacity
              style={[styles.actionButton, styles.homeButton]}
              onPress={() => router.push('/')}
            >
              <IconSymbol name="house.fill" size={20} color="#FFFFFF" />
              <ThemedText style={styles.actionButtonText}>Home</ThemedText>
            </TouchableOpacity>
          </View>
        </View>
      )}
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingTop: 60,
    paddingBottom: 20,
  },
  videoContainer: {
    aspectRatio: 16/9,
    backgroundColor: '#000',
    marginHorizontal: 20,
    borderRadius: 12,
    overflow: 'hidden',
  },
  video: {
    flex: 1,
  },
  qualityContainer: {
    padding: 20,
  },
  qualityOptions: {
    marginTop: 20,
    gap: 10,
  },
  qualityOption: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 15,
    borderRadius: 10,
    backgroundColor: 'rgba(0,0,0,0.05)',
  },
  selectedQualityOption: {
    backgroundColor: Colors.light.tint,
  },
  qualityName: {
    fontWeight: 'bold',
  },
  qualityDetails: {
    opacity: 0.7,
  },
  selectedQualityText: {
    color: '#FFFFFF',
  },
  exportButtonContainer: {
    padding: 20,
    marginTop: 'auto',
  },
  exportButton: {
    flexDirection: 'row',
    backgroundColor: Colors.light.tint,
    padding: 15,
    borderRadius: 10,
    justifyContent: 'center',
    alignItems: 'center',
    gap: 10,
  },
  exportButtonText: {
    color: '#FFFFFF',
    fontWeight: 'bold',
    fontSize: 16,
  },
  exportingContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    gap: 10,
  },
  exportingText: {
    fontWeight: 'bold',
  },
  successContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  successIconContainer: {
    marginBottom: 20,
  },
  successText: {
    textAlign: 'center',
    marginTop: 10,
    marginBottom: 40,
  },
  actionButtonsContainer: {
    flexDirection: 'row',
    gap: 20,
  },
  actionButton: {
    flexDirection: 'row',
    backgroundColor: Colors.light.tint,
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderRadius: 10,
    alignItems: 'center',
    gap: 8,
  },
  homeButton: {
    backgroundColor: 'rgba(0,0,0,0.7)',
  },
  actionButtonText: {
    color: '#FFFFFF',
    fontWeight: 'bold',
  },
});
