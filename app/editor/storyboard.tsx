import React, { useState, useEffect, useRef } from 'react';
import { StyleSheet, View, ScrollView, TouchableOpacity, Image, Alert, ActivityIndicator } from 'react-native';
import { router, useLocalSearchParams } from 'expo-router';
import * as FileSystem from 'expo-file-system';
import { Video } from 'expo-av';
import Animated, { useSharedValue, useAnimatedStyle, withTiming } from 'react-native-reanimated';

import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { Colors } from '@/constants/Colors';
import { useColorScheme } from '@/hooks/useColorScheme';

// This would be replaced with actual ML service in a real implementation
const mockAnalyzeVideo = async (uri: string) => {
  // Simulate processing time
  await new Promise(resolve => setTimeout(resolve, 1500));
  
  // Return mock data
  return {
    interestScore: Math.random() * 100,
    highlights: [
      { startTime: 0, endTime: 3, score: Math.random() * 100 },
      { startTime: 5, endTime: 8, score: Math.random() * 100 },
      { startTime: 12, endTime: 15, score: Math.random() * 100 },
    ],
  };
};

type VideoClip = {
  uri: string;
  interestScore: number;
  highlights: Array<{ startTime: number; endTime: number; score: number }>;
  thumbnailUri?: string;
  duration?: number;
};

export default function StoryboardScreen() {
  const params = useLocalSearchParams();
  const colorScheme = useColorScheme();
  const [videoClips, setVideoClips] = useState<VideoClip[]>([]);
  const [loading, setLoading] = useState(true);
  const [analyzing, setAnalyzing] = useState(false);
  const [currentStep, setCurrentStep] = useState<'loading' | 'analyzing' | 'ready'>('loading');
  const [progress, setProgress] = useState(0);
  const progressAnim = useSharedValue(0);
  
  const scrollViewRef = useRef<ScrollView>(null);

  useEffect(() => {
    if (params.uris) {
      const uriList = (params.uris as string).split(',');
      loadVideos(uriList);
    } else {
      Alert.alert('Error', 'No videos provided');
      router.back();
    }
  }, [params.uris]);

  const loadVideos = async (uriList: string[]) => {
    setLoading(true);
    setCurrentStep('loading');
    
    try {
      // Load basic video info
      const clips: VideoClip[] = uriList.map(uri => ({
        uri,
        interestScore: 0,
        highlights: [],
      }));
      
      setVideoClips(clips);
      setLoading(false);
      
      // Start analysis
      analyzeVideos(clips);
    } catch (error) {
      console.error('Error loading videos:', error);
      Alert.alert('Error', 'Failed to load videos');
      setLoading(false);
    }
  };

  const analyzeVideos = async (clips: VideoClip[]) => {
    setAnalyzing(true);
    setCurrentStep('analyzing');
    
    try {
      const analyzedClips = [...clips];
      
      for (let i = 0; i < clips.length; i++) {
        // Update progress
        const progressValue = (i / clips.length) * 100;
        setProgress(progressValue);
        progressAnim.value = withTiming(progressValue / 100, { duration: 300 });
        
        // Analyze video
        const analysis = await mockAnalyzeVideo(clips[i].uri);
        
        // Generate thumbnail (in a real app, this would extract from the video)
        const thumbnailUri = clips[i].uri;
        
        analyzedClips[i] = {
          ...clips[i],
          ...analysis,
          thumbnailUri,
        };
      }
      
      // Sort by interest score (highest first)
      analyzedClips.sort((a, b) => b.interestScore - a.interestScore);
      
      setVideoClips(analyzedClips);
      setProgress(100);
      progressAnim.value = withTiming(1, { duration: 300 });
      
      // Delay to show 100% progress
      setTimeout(() => {
        setAnalyzing(false);
        setCurrentStep('ready');
      }, 500);
      
    } catch (error) {
      console.error('Error analyzing videos:', error);
      Alert.alert('Error', 'Failed to analyze videos');
      setAnalyzing(false);
      setCurrentStep('ready');
    }
  };

  const handleClipPress = (index: number) => {
    // In a real app, this would show a preview or allow editing
    console.log('Clip pressed:', videoClips[index]);
  };

  const handleAutoEdit = () => {
    // In a real app, this would create an auto-edited video
    // For now, just navigate to the edit screen with the first video
    if (videoClips.length > 0) {
      router.push({
        pathname: '/editor/edit',
        params: { uri: videoClips[0].uri }
      });
    }
  };

  const progressBarStyle = useAnimatedStyle(() => {
    return {
      width: `${progressAnim.value * 100}%`,
    };
  });

  const renderLoadingState = () => (
    <View style={styles.loadingContainer}>
      <ActivityIndicator size="large" color={Colors[colorScheme ?? 'light'].tint} />
      <ThemedText style={styles.loadingText}>Loading videos...</ThemedText>
    </View>
  );

  const renderAnalyzingState = () => (
    <View style={styles.loadingContainer}>
      <ThemedText style={styles.loadingText}>Analyzing videos...</ThemedText>
      <View style={styles.progressBarContainer}>
        <Animated.View style={[styles.progressBar, progressBarStyle]} />
      </View>
      <ThemedText style={styles.progressText}>{Math.round(progress)}%</ThemedText>
    </View>
  );

  const renderStoryboard = () => (
    <>
      <ScrollView
        ref={scrollViewRef}
        horizontal
        contentContainerStyle={styles.storyboardContainer}
        showsHorizontalScrollIndicator={false}
      >
        {videoClips.map((clip, index) => (
          <TouchableOpacity
            key={index}
            style={[
              styles.clipContainer,
              { width: 100 + (clip.interestScore / 2) } // Size based on interest score
            ]}
            onPress={() => handleClipPress(index)}
          >
            <Image
              source={{ uri: clip.thumbnailUri || clip.uri }}
              style={styles.clipThumbnail}
            />
            <View style={styles.clipInfo}>
              <ThemedText style={styles.clipScore}>
                {Math.round(clip.interestScore)}
              </ThemedText>
            </View>
          </TouchableOpacity>
        ))}
      </ScrollView>
      
      <View style={styles.actionsContainer}>
        <TouchableOpacity
          style={styles.actionButton}
          onPress={() => router.back()}
        >
          <IconSymbol name="arrow.left" size={20} color="#FFFFFF" />
          <ThemedText style={styles.actionButtonText}>Back</ThemedText>
        </TouchableOpacity>
        
        <TouchableOpacity
          style={[styles.actionButton, styles.primaryButton]}
          onPress={handleAutoEdit}
        >
          <IconSymbol name="wand.and.stars" size={20} color="#FFFFFF" />
          <ThemedText style={styles.actionButtonText}>Auto-Edit</ThemedText>
        </TouchableOpacity>
        
        <TouchableOpacity
          style={styles.actionButton}
          onPress={() => {
            if (videoClips.length > 0) {
              router.push({
                pathname: '/editor/edit',
                params: { uri: videoClips[0].uri }
              });
            }
          }}
        >
          <IconSymbol name="slider.horizontal.3" size={20} color="#FFFFFF" />
          <ThemedText style={styles.actionButtonText}>Manual Edit</ThemedText>
        </TouchableOpacity>
      </View>
    </>
  );

  return (
    <ThemedView style={styles.container}>
      <View style={styles.header}>
        <ThemedText type="subtitle">Video Storyboard</ThemedText>
      </View>

      {currentStep === 'loading' && renderLoadingState()}
      {currentStep === 'analyzing' && renderAnalyzingState()}
      {currentStep === 'ready' && renderStoryboard()}
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    paddingTop: 60,
    paddingHorizontal: 20,
    paddingBottom: 20,
    alignItems: 'center',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  loadingText: {
    marginTop: 20,
    fontSize: 16,
  },
  progressBarContainer: {
    width: '80%',
    height: 8,
    backgroundColor: 'rgba(0,0,0,0.1)',
    borderRadius: 4,
    marginTop: 20,
    overflow: 'hidden',
  },
  progressBar: {
    height: '100%',
    backgroundColor: Colors.light.tint,
  },
  progressText: {
    marginTop: 10,
    fontSize: 14,
  },
  storyboardContainer: {
    padding: 20,
    gap: 10,
  },
  clipContainer: {
    height: 150,
    borderRadius: 8,
    overflow: 'hidden',
    marginRight: 10,
  },
  clipThumbnail: {
    width: '100%',
    height: '100%',
  },
  clipInfo: {
    position: 'absolute',
    bottom: 5,
    right: 5,
    backgroundColor: 'rgba(0,0,0,0.7)',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  clipScore: {
    color: '#FFFFFF',
    fontSize: 12,
  },
  actionsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    padding: 20,
    borderTopWidth: 1,
    borderTopColor: 'rgba(0,0,0,0.1)',
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(0,0,0,0.7)',
    paddingHorizontal: 15,
    paddingVertical: 10,
    borderRadius: 20,
    gap: 8,
  },
  primaryButton: {
    backgroundColor: Colors.light.tint,
  },
  actionButtonText: {
    color: '#FFFFFF',
    fontWeight: 'bold',
  },
});
