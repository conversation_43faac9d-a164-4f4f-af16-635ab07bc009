import React, { useState, useEffect, useRef } from 'react';
import { StyleSheet, View, TouchableOpacity, ScrollView, Alert, ActivityIndicator } from 'react-native';
import { router, useLocalSearchParams } from 'expo-router';
import { Video, AVPlaybackStatus, ResizeMode } from 'expo-av';
import Slider from '@react-native-community/slider';
import * as FileSystem from 'expo-file-system';
import { LinearGradient } from 'expo-linear-gradient';
import { BottomSheetModal, BottomSheetModalProvider } from '@gorhom/bottom-sheet';

import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { Colors } from '@/constants/Colors';
import { useColorScheme } from '@/hooks/useColorScheme';

// Mock filters - in a real app, these would be actual FFmpeg filters
const videoFilters = [
  { id: 'original', name: 'Original', filter: '' },
  { id: 'warm', name: 'Warm', filter: 'colortemperature=4000K' },
  { id: 'cool', name: 'Cool', filter: 'colortemperature=10000K' },
  { id: 'vintage', name: 'Vintage', filter: 'curves=vintage' },
  { id: 'bw', name: 'B&W', filter: 'hue=s=0' },
  { id: 'vivid', name: 'Vivid', filter: 'eq=saturation=1.5:contrast=1.2' },
];

export default function EditScreen() {
  const params = useLocalSearchParams();
  const colorScheme = useColorScheme();
  const videoRef = useRef<Video>(null);
  const bottomSheetRef = useRef<BottomSheetModal>(null);
  
  const [videoUri, setVideoUri] = useState<string | null>(null);
  const [status, setStatus] = useState<AVPlaybackStatus | null>(null);
  const [loading, setLoading] = useState(true);
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(0);
  const [isSeeking, setIsSeeking] = useState(false);
  const [selectedFilter, setSelectedFilter] = useState('original');
  const [trimStart, setTrimStart] = useState(0);
  const [trimEnd, setTrimEnd] = useState(0);
  const [isProcessing, setIsProcessing] = useState(false);
  const [activeTab, setActiveTab] = useState<'trim' | 'filter' | 'text'>('trim');

  useEffect(() => {
    if (params.uri) {
      setVideoUri(params.uri as string);
    } else {
      Alert.alert('Error', 'No video provided');
      router.back();
    }
  }, [params.uri]);

  const onPlaybackStatusUpdate = (status: AVPlaybackStatus) => {
    setStatus(status);
    if (status.isLoaded) {
      if (!duration && status.durationMillis) {
        setDuration(status.durationMillis / 1000);
        setTrimEnd(status.durationMillis / 1000);
      }
      if (!isSeeking && status.positionMillis) {
        setCurrentTime(status.positionMillis / 1000);
      }
    }
  };

  const handlePlayPause = () => {
    if (videoRef.current) {
      if (status?.isLoaded && status.isPlaying) {
        videoRef.current.pauseAsync();
      } else {
        videoRef.current.playAsync();
      }
    }
  };

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs < 10 ? '0' : ''}${secs}`;
  };

  const handleSliderValueChange = (value: number) => {
    setIsSeeking(true);
    setCurrentTime(value);
  };

  const handleSliderSlidingComplete = (value: number) => {
    setIsSeeking(false);
    if (videoRef.current) {
      videoRef.current.setPositionAsync(value * 1000);
    }
  };

  const handleTrimStartChange = (value: number) => {
    if (value < trimEnd - 1) {
      setTrimStart(value);
    }
  };

  const handleTrimEndChange = (value: number) => {
    if (value > trimStart + 1) {
      setTrimEnd(value);
    }
  };

  const handleFilterSelect = (filterId: string) => {
    setSelectedFilter(filterId);
    // In a real app, this would apply the filter using FFmpeg
  };

  const handleExport = () => {
    // In a real app, this would use FFmpeg to process the video
    setIsProcessing(true);
    
    // Simulate processing
    setTimeout(() => {
      setIsProcessing(false);
      Alert.alert(
        'Export Complete',
        'Your video has been saved to the gallery',
        [
          { text: 'OK', onPress: () => router.push('/') }
        ]
      );
    }, 3000);
  };

  const showBottomSheet = () => {
    bottomSheetRef.current?.present();
  };

  if (!videoUri || loading) {
    return (
      <ThemedView style={styles.container}>
        <ActivityIndicator size="large" color={Colors[colorScheme ?? 'light'].tint} />
      </ThemedView>
    );
  }

  if (isProcessing) {
    return (
      <ThemedView style={styles.container}>
        <ActivityIndicator size="large" color={Colors[colorScheme ?? 'light'].tint} />
        <ThemedText style={styles.processingText}>Processing video...</ThemedText>
      </ThemedView>
    );
  }

  return (
    <BottomSheetModalProvider>
      <ThemedView style={styles.container}>
        <View style={styles.header}>
          <TouchableOpacity onPress={() => router.back()}>
            <IconSymbol 
              name="chevron.left" 
              size={24} 
              color={Colors[colorScheme ?? 'light'].text} 
            />
          </TouchableOpacity>
          <ThemedText type="subtitle">Edit Video</ThemedText>
          <TouchableOpacity onPress={handleExport}>
            <ThemedText style={styles.exportText}>Export</ThemedText>
          </TouchableOpacity>
        </View>

        <View style={styles.videoContainer}>
          <Video
            ref={videoRef}
            style={styles.video}
            source={{ uri: videoUri }}
            resizeMode={ResizeMode.CONTAIN}
            isLooping
            onPlaybackStatusUpdate={onPlaybackStatusUpdate}
            onLoad={() => setLoading(false)}
            onError={(error) => {
              console.error('Video error:', error);
              Alert.alert('Error', 'Failed to load video');
            }}
          />
          
          <LinearGradient
            colors={['rgba(0,0,0,0.7)', 'transparent']}
            style={styles.videoOverlayTop}
          />
          
          <LinearGradient
            colors={['transparent', 'rgba(0,0,0,0.7)']}
            style={styles.videoOverlayBottom}
          >
            <TouchableOpacity 
              style={styles.playButton}
              onPress={handlePlayPause}
            >
              <IconSymbol 
                name={status?.isLoaded && status.isPlaying ? "pause.fill" : "play.fill"} 
                size={24} 
                color="#FFFFFF" 
              />
            </TouchableOpacity>
            
            <View style={styles.timelineContainer}>
              <ThemedText style={styles.timeText}>{formatTime(currentTime)}</ThemedText>
              <Slider
                style={styles.slider}
                minimumValue={0}
                maximumValue={duration}
                value={currentTime}
                minimumTrackTintColor={Colors[colorScheme ?? 'light'].tint}
                maximumTrackTintColor="rgba(255,255,255,0.3)"
                thumbTintColor={Colors[colorScheme ?? 'light'].tint}
                onValueChange={handleSliderValueChange}
                onSlidingComplete={handleSliderSlidingComplete}
              />
              <ThemedText style={styles.timeText}>{formatTime(duration)}</ThemedText>
            </View>
          </LinearGradient>
        </View>

        <View style={styles.tabsContainer}>
          <TouchableOpacity 
            style={[styles.tab, activeTab === 'trim' && styles.activeTab]}
            onPress={() => setActiveTab('trim')}
          >
            <IconSymbol 
              name="scissors" 
              size={20} 
              color={activeTab === 'trim' ? Colors[colorScheme ?? 'light'].tint : Colors[colorScheme ?? 'light'].text} 
            />
            <ThemedText style={activeTab === 'trim' && styles.activeTabText}>Trim</ThemedText>
          </TouchableOpacity>
          
          <TouchableOpacity 
            style={[styles.tab, activeTab === 'filter' && styles.activeTab]}
            onPress={() => setActiveTab('filter')}
          >
            <IconSymbol 
              name="camera.filters" 
              size={20} 
              color={activeTab === 'filter' ? Colors[colorScheme ?? 'light'].tint : Colors[colorScheme ?? 'light'].text} 
            />
            <ThemedText style={activeTab === 'filter' && styles.activeTabText}>Filter</ThemedText>
          </TouchableOpacity>
          
          <TouchableOpacity 
            style={[styles.tab, activeTab === 'text' && styles.activeTab]}
            onPress={() => setActiveTab('text')}
          >
            <IconSymbol 
              name="textformat" 
              size={20} 
              color={activeTab === 'text' ? Colors[colorScheme ?? 'light'].tint : Colors[colorScheme ?? 'light'].text} 
            />
            <ThemedText style={activeTab === 'text' && styles.activeTabText}>Text</ThemedText>
          </TouchableOpacity>
        </View>

        {activeTab === 'trim' && (
          <View style={styles.trimContainer}>
            <ThemedText>Trim Start: {formatTime(trimStart)}</ThemedText>
            <Slider
              style={styles.trimSlider}
              minimumValue={0}
              maximumValue={duration}
              value={trimStart}
              minimumTrackTintColor="rgba(0,0,0,0.3)"
              maximumTrackTintColor={Colors[colorScheme ?? 'light'].tint}
              thumbTintColor={Colors[colorScheme ?? 'light'].tint}
              onValueChange={handleTrimStartChange}
            />
            
            <ThemedText>Trim End: {formatTime(trimEnd)}</ThemedText>
            <Slider
              style={styles.trimSlider}
              minimumValue={0}
              maximumValue={duration}
              value={trimEnd}
              minimumTrackTintColor={Colors[colorScheme ?? 'light'].tint}
              maximumTrackTintColor="rgba(0,0,0,0.3)"
              thumbTintColor={Colors[colorScheme ?? 'light'].tint}
              onValueChange={handleTrimEndChange}
            />
          </View>
        )}

        {activeTab === 'filter' && (
          <ScrollView 
            horizontal 
            style={styles.filtersContainer}
            contentContainerStyle={styles.filtersContent}
            showsHorizontalScrollIndicator={false}
          >
            {videoFilters.map((filter) => (
              <TouchableOpacity
                key={filter.id}
                style={[
                  styles.filterItem,
                  selectedFilter === filter.id && styles.selectedFilterItem,
                ]}
                onPress={() => handleFilterSelect(filter.id)}
              >
                <ThemedText style={styles.filterName}>{filter.name}</ThemedText>
              </TouchableOpacity>
            ))}
          </ScrollView>
        )}

        {activeTab === 'text' && (
          <View style={styles.textContainer}>
            <TouchableOpacity 
              style={styles.addTextButton}
              onPress={showBottomSheet}
            >
              <IconSymbol name="plus" size={20} color="#FFFFFF" />
              <ThemedText style={styles.addTextButtonText}>Add Text</ThemedText>
            </TouchableOpacity>
            
            <ThemedText style={styles.textHint}>
              Tap to add title, caption, or subtitle to your video
            </ThemedText>
          </View>
        )}

        <BottomSheetModal
          ref={bottomSheetRef}
          index={0}
          snapPoints={['50%']}
          backgroundStyle={{
            backgroundColor: Colors[colorScheme ?? 'light'].background,
          }}
        >
          <View style={styles.bottomSheetContent}>
            <ThemedText type="subtitle">Add Text</ThemedText>
            {/* Text editor would go here in a real app */}
            <ThemedText>Text editor coming soon!</ThemedText>
          </View>
        </BottomSheetModal>
      </ThemedView>
    </BottomSheetModalProvider>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingTop: 60,
    paddingBottom: 20,
  },
  exportText: {
    color: Colors.light.tint,
    fontWeight: 'bold',
  },
  videoContainer: {
    aspectRatio: 16/9,
    backgroundColor: '#000',
    position: 'relative',
  },
  video: {
    flex: 1,
  },
  videoOverlayTop: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    height: 60,
  },
  videoOverlayBottom: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    height: 80,
    justifyContent: 'center',
    alignItems: 'center',
  },
  playButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(0,0,0,0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 10,
  },
  timelineContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    width: '100%',
    paddingHorizontal: 20,
  },
  slider: {
    flex: 1,
    height: 40,
  },
  timeText: {
    color: '#FFFFFF',
    fontSize: 12,
    width: 40,
    textAlign: 'center',
  },
  tabsContainer: {
    flexDirection: 'row',
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.1)',
  },
  tab: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 15,
    gap: 8,
  },
  activeTab: {
    borderBottomWidth: 2,
    borderBottomColor: Colors.light.tint,
  },
  activeTabText: {
    color: Colors.light.tint,
    fontWeight: 'bold',
  },
  trimContainer: {
    padding: 20,
  },
  trimSlider: {
    height: 40,
    marginBottom: 20,
  },
  filtersContainer: {
    padding: 20,
  },
  filtersContent: {
    gap: 10,
  },
  filterItem: {
    padding: 10,
    borderRadius: 8,
    backgroundColor: 'rgba(0,0,0,0.1)',
    marginRight: 10,
  },
  selectedFilterItem: {
    backgroundColor: Colors.light.tint,
  },
  filterName: {
    fontWeight: 'bold',
  },
  textContainer: {
    padding: 20,
    alignItems: 'center',
  },
  addTextButton: {
    flexDirection: 'row',
    backgroundColor: Colors.light.tint,
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 20,
    alignItems: 'center',
    gap: 8,
  },
  addTextButtonText: {
    color: '#FFFFFF',
    fontWeight: 'bold',
  },
  textHint: {
    marginTop: 20,
    textAlign: 'center',
    opacity: 0.7,
  },
  bottomSheetContent: {
    padding: 20,
  },
  processingText: {
    marginTop: 20,
  },
});
