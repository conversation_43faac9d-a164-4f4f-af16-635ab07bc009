import React, { useState, useEffect } from 'react';
import { StyleSheet, View, FlatList, TouchableOpacity, Image, Alert, ActivityIndicator } from 'react-native';
import { router } from 'expo-router';
import * as FileSystem from 'expo-file-system';
import AsyncStorage from '@react-native-async-storage/async-storage';

import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { Colors } from '@/constants/Colors';
import { useColorScheme } from '@/hooks/useColorScheme';

// Interface for project
interface Project {
  id: string;
  name: string;
  thumbnailUri: string;
  videoUri: string;
  duration: number;
  createdAt: number;
  updatedAt: number;
}

export default function ProjectsScreen() {
  const colorScheme = useColorScheme();
  const [projects, setProjects] = useState<Project[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadProjects();
  }, []);

  const loadProjects = async () => {
    setLoading(true);
    try {
      // Load projects from AsyncStorage
      const projectsJson = await AsyncStorage.getItem('video_editor_projects');
      
      if (projectsJson) {
        const loadedProjects = JSON.parse(projectsJson) as Project[];
        setProjects(loadedProjects);
      } else {
        // No projects yet
        setProjects([]);
      }
    } catch (error) {
      console.error('Error loading projects:', error);
      Alert.alert('Error', 'Failed to load projects');
    } finally {
      setLoading(false);
    }
  };

  const handleProjectPress = (project: Project) => {
    router.push({
      pathname: '/editor/edit',
      params: { uri: project.videoUri, projectId: project.id }
    });
  };

  const handleDeleteProject = async (projectId: string) => {
    Alert.alert(
      'Delete Project',
      'Are you sure you want to delete this project?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            try {
              // Remove project from list
              const updatedProjects = projects.filter(p => p.id !== projectId);
              setProjects(updatedProjects);
              
              // Save updated projects list
              await AsyncStorage.setItem('video_editor_projects', JSON.stringify(updatedProjects));
              
              // Delete project files
              const project = projects.find(p => p.id === projectId);
              if (project) {
                // Delete thumbnail if it's in our app's directory
                if (project.thumbnailUri.startsWith(FileSystem.documentDirectory || '')) {
                  await FileSystem.deleteAsync(project.thumbnailUri).catch(() => {});
                }
                
                // Delete video if it's in our app's directory
                if (project.videoUri.startsWith(FileSystem.documentDirectory || '')) {
                  await FileSystem.deleteAsync(project.videoUri).catch(() => {});
                }
              }
            } catch (error) {
              console.error('Error deleting project:', error);
              Alert.alert('Error', 'Failed to delete project');
            }
          }
        }
      ]
    );
  };

  const formatDuration = (seconds: number) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = Math.floor(seconds % 60);
    return `${minutes}:${remainingSeconds < 10 ? '0' : ''}${remainingSeconds}`;
  };

  const formatDate = (timestamp: number) => {
    const date = new Date(timestamp);
    return date.toLocaleDateString();
  };

  if (loading) {
    return (
      <ThemedView style={styles.container}>
        <View style={styles.header}>
          <TouchableOpacity onPress={() => router.back()}>
            <IconSymbol 
              name="chevron.left" 
              size={24} 
              color={Colors[colorScheme ?? 'light'].text} 
            />
          </TouchableOpacity>
          <ThemedText type="subtitle">My Projects</ThemedText>
          <View style={{ width: 24 }} />
        </View>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={Colors[colorScheme ?? 'light'].tint} />
        </View>
      </ThemedView>
    );
  }

  return (
    <ThemedView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity onPress={() => router.back()}>
          <IconSymbol 
            name="chevron.left" 
            size={24} 
            color={Colors[colorScheme ?? 'light'].text} 
          />
        </TouchableOpacity>
        <ThemedText type="subtitle">My Projects</ThemedText>
        <View style={{ width: 24 }} />
      </View>

      {projects.length === 0 ? (
        <View style={styles.emptyContainer}>
          <IconSymbol 
            name="folder" 
            size={60} 
            color={Colors[colorScheme ?? 'light'].text} 
            style={{ opacity: 0.5 }}
          />
          <ThemedText style={styles.emptyText}>No projects yet</ThemedText>
          <TouchableOpacity 
            style={styles.createButton}
            onPress={() => router.push('/editor/gallery')}
          >
            <IconSymbol name="plus" size={20} color="#FFFFFF" />
            <ThemedText style={styles.createButtonText}>Create New Project</ThemedText>
          </TouchableOpacity>
        </View>
      ) : (
        <FlatList
          data={projects}
          keyExtractor={(item) => item.id}
          renderItem={({ item }) => (
            <TouchableOpacity
              style={styles.projectItem}
              onPress={() => handleProjectPress(item)}
            >
              <Image source={{ uri: item.thumbnailUri }} style={styles.thumbnail} />
              <View style={styles.projectInfo}>
                <ThemedText type="defaultSemiBold">{item.name}</ThemedText>
                <ThemedText style={styles.projectDetails}>
                  {formatDuration(item.duration)} • {formatDate(item.updatedAt)}
                </ThemedText>
              </View>
              <TouchableOpacity
                style={styles.deleteButton}
                onPress={() => handleDeleteProject(item.id)}
              >
                <IconSymbol name="trash" size={20} color={Colors[colorScheme ?? 'light'].text} />
              </TouchableOpacity>
            </TouchableOpacity>
          )}
          contentContainerStyle={styles.listContainer}
        />
      )}
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingTop: 60,
    paddingBottom: 20,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  emptyText: {
    marginTop: 20,
    marginBottom: 30,
    opacity: 0.5,
  },
  createButton: {
    flexDirection: 'row',
    backgroundColor: Colors.light.tint,
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderRadius: 10,
    alignItems: 'center',
    gap: 8,
  },
  createButtonText: {
    color: '#FFFFFF',
    fontWeight: 'bold',
  },
  listContainer: {
    padding: 20,
  },
  projectItem: {
    flexDirection: 'row',
    backgroundColor: 'rgba(0,0,0,0.05)',
    borderRadius: 10,
    overflow: 'hidden',
    marginBottom: 15,
  },
  thumbnail: {
    width: 100,
    height: 70,
  },
  projectInfo: {
    flex: 1,
    padding: 10,
    justifyContent: 'center',
  },
  projectDetails: {
    fontSize: 12,
    opacity: 0.7,
    marginTop: 5,
  },
  deleteButton: {
    padding: 15,
    justifyContent: 'center',
    alignItems: 'center',
  },
});
