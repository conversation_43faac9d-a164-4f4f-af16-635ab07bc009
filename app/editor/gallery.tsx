import React, { useState, useEffect } from 'react';
import { StyleSheet, View, FlatList, TouchableOpacity, Image, Alert, ActivityIndicator } from 'react-native';
import { router } from 'expo-router';
import * as MediaLibrary from 'expo-media-library';
import * as ImagePicker from 'expo-image-picker';

import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { Colors } from '@/constants/Colors';
import { useColorScheme } from '@/hooks/useColorScheme';

type MediaAsset = {
  id: string;
  uri: string;
  filename: string;
  duration: number;
  creationTime: number;
};

export default function GalleryScreen() {
  const colorScheme = useColorScheme();
  const [hasPermission, setHasPermission] = useState<boolean | null>(null);
  const [mediaAssets, setMediaAssets] = useState<MediaAsset[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedAssets, setSelectedAssets] = useState<string[]>([]);

  useEffect(() => {
    (async () => {
      const { status } = await MediaLibrary.requestPermissionsAsync();
      setHasPermission(status === 'granted');
      
      if (status === 'granted') {
        loadMediaAssets();
      } else {
        setLoading(false);
      }
    })();
  }, []);

  const loadMediaAssets = async () => {
    setLoading(true);
    try {
      // Get only videos
      const media = await MediaLibrary.getAssetsAsync({
        mediaType: 'video',
        first: 50, // Limit to 50 most recent videos
        sortBy: ['creationTime'],
      });
      
      const assets: MediaAsset[] = media.assets.map(asset => ({
        id: asset.id,
        uri: asset.uri,
        filename: asset.filename,
        duration: asset.duration || 0,
        creationTime: asset.creationTime || Date.now(),
      }));
      
      setMediaAssets(assets);
    } catch (error) {
      console.error('Error loading media assets:', error);
      Alert.alert('Error', 'Failed to load videos from gallery');
    } finally {
      setLoading(false);
    }
  };

  const handleAssetPress = (id: string) => {
    if (selectedAssets.includes(id)) {
      setSelectedAssets(selectedAssets.filter(assetId => assetId !== id));
    } else {
      setSelectedAssets([...selectedAssets, id]);
    }
  };

  const handleImportPress = async () => {
    if (selectedAssets.length === 0) {
      Alert.alert('Selection Required', 'Please select at least one video');
      return;
    }

    const selectedVideos = mediaAssets.filter(asset => selectedAssets.includes(asset.id));
    
    if (selectedVideos.length === 1) {
      // If only one video, go directly to edit screen
      router.push({
        pathname: '/editor/edit',
        params: { uri: selectedVideos[0].uri }
      });
    } else {
      // If multiple videos, go to storyboard screen
      const uris = selectedVideos.map(video => video.uri).join(',');
      router.push({
        pathname: '/editor/storyboard',
        params: { uris }
      });
    }
  };

  const pickVideo = async () => {
    try {
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Videos,
        allowsMultipleSelection: true,
        quality: 1,
      });

      if (!result.canceled && result.assets.length > 0) {
        if (result.assets.length === 1) {
          router.push({
            pathname: '/editor/edit',
            params: { uri: result.assets[0].uri }
          });
        } else {
          const uris = result.assets.map(asset => asset.uri).join(',');
          router.push({
            pathname: '/editor/storyboard',
            params: { uris }
          });
        }
      }
    } catch (error) {
      console.error('Error picking video:', error);
      Alert.alert('Error', 'Failed to pick video from gallery');
    }
  };

  const formatDuration = (seconds: number) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = Math.floor(seconds % 60);
    return `${minutes}:${remainingSeconds < 10 ? '0' : ''}${remainingSeconds}`;
  };

  if (hasPermission === null) {
    return (
      <ThemedView style={styles.container}>
        <ActivityIndicator size="large" color={Colors[colorScheme ?? 'light'].tint} />
      </ThemedView>
    );
  }

  if (hasPermission === false) {
    return (
      <ThemedView style={styles.container}>
        <ThemedText>No access to media library</ThemedText>
        <TouchableOpacity 
          style={styles.button} 
          onPress={() => router.back()}
        >
          <ThemedText>Go Back</ThemedText>
        </TouchableOpacity>
      </ThemedView>
    );
  }

  return (
    <ThemedView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity onPress={() => router.back()}>
          <IconSymbol 
            name="chevron.left" 
            size={24} 
            color={Colors[colorScheme ?? 'light'].text} 
          />
        </TouchableOpacity>
        <ThemedText type="subtitle">Select Videos</ThemedText>
        <TouchableOpacity onPress={pickVideo}>
          <IconSymbol 
            name="folder" 
            size={24} 
            color={Colors[colorScheme ?? 'light'].tint} 
          />
        </TouchableOpacity>
      </View>

      {loading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={Colors[colorScheme ?? 'light'].tint} />
        </View>
      ) : (
        <>
          <FlatList
            data={mediaAssets}
            keyExtractor={(item) => item.id}
            numColumns={3}
            renderItem={({ item }) => (
              <TouchableOpacity
                style={[
                  styles.mediaItem,
                  selectedAssets.includes(item.id) && styles.selectedMediaItem,
                ]}
                onPress={() => handleAssetPress(item.id)}
              >
                <Image source={{ uri: item.uri }} style={styles.thumbnail} />
                <View style={styles.durationBadge}>
                  <ThemedText style={styles.durationText}>
                    {formatDuration(item.duration)}
                  </ThemedText>
                </View>
                {selectedAssets.includes(item.id) && (
                  <View style={styles.checkmarkContainer}>
                    <IconSymbol name="checkmark.circle.fill" size={24} color="#FFFFFF" />
                  </View>
                )}
              </TouchableOpacity>
            )}
            contentContainerStyle={styles.gridContainer}
          />

          {selectedAssets.length > 0 && (
            <View style={styles.bottomBar}>
              <TouchableOpacity 
                style={styles.importButton}
                onPress={handleImportPress}
              >
                <ThemedText style={styles.importButtonText}>
                  Import {selectedAssets.length} {selectedAssets.length === 1 ? 'Video' : 'Videos'}
                </ThemedText>
              </TouchableOpacity>
            </View>
          )}
        </>
      )}
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingTop: 60,
    paddingBottom: 20,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  gridContainer: {
    padding: 5,
  },
  mediaItem: {
    flex: 1/3,
    aspectRatio: 1,
    margin: 2,
    borderRadius: 8,
    overflow: 'hidden',
  },
  selectedMediaItem: {
    borderWidth: 2,
    borderColor: Colors.light.tint,
  },
  thumbnail: {
    width: '100%',
    height: '100%',
  },
  durationBadge: {
    position: 'absolute',
    bottom: 5,
    right: 5,
    backgroundColor: 'rgba(0,0,0,0.7)',
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 4,
  },
  durationText: {
    color: '#FFFFFF',
    fontSize: 10,
  },
  checkmarkContainer: {
    position: 'absolute',
    top: 5,
    right: 5,
  },
  bottomBar: {
    padding: 20,
    backgroundColor: 'rgba(255,255,255,0.9)',
    borderTopWidth: 1,
    borderTopColor: 'rgba(0,0,0,0.1)',
  },
  importButton: {
    backgroundColor: Colors.light.tint,
    padding: 15,
    borderRadius: 10,
    alignItems: 'center',
  },
  importButtonText: {
    color: '#FFFFFF',
    fontWeight: 'bold',
  },
  button: {
    marginTop: 20,
    backgroundColor: '#2196F3',
    padding: 15,
    borderRadius: 10,
  },
});
