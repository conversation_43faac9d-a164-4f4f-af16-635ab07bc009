import React from 'react';
import { TouchableOpacity, StyleSheet, ViewStyle } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import * as Haptics from 'expo-haptics';

import { ThemedView } from '@/components/ThemedView';
import { Colors, Gradients, Shadows } from '@/constants/Colors';
import { useColorScheme } from '@/hooks/useColorScheme';

export interface CardProps {
  children: React.ReactNode;
  onPress?: () => void;
  variant?: 'default' | 'elevated' | 'outlined' | 'gradient';
  padding?: 'none' | 'small' | 'medium' | 'large';
  style?: ViewStyle;
  hapticFeedback?: boolean;
}

export function Card({
  children,
  onPress,
  variant = 'default',
  padding = 'medium',
  style,
  hapticFeedback = true,
}: CardProps) {
  const colorScheme = useColorScheme();
  const colors = Colors[colorScheme ?? 'light'];

  const handlePress = () => {
    if (!onPress) return;
    
    if (hapticFeedback) {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    }
    
    onPress();
  };

  const getCardStyle = (): ViewStyle => {
    const baseStyle: ViewStyle = {
      ...styles.base,
      ...styles[padding],
    };

    switch (variant) {
      case 'elevated':
        return {
          ...baseStyle,
          backgroundColor: colors.cardBackground,
          ...Shadows.medium,
        };
      case 'outlined':
        return {
          ...baseStyle,
          backgroundColor: colors.cardBackground,
          borderWidth: 1,
          borderColor: colors.border,
        };
      case 'gradient':
        return {
          ...baseStyle,
          backgroundColor: 'transparent',
          ...Shadows.large,
        };
      default:
        return {
          ...baseStyle,
          backgroundColor: colors.cardBackground,
          ...Shadows.small,
        };
    }
  };

  const cardStyle = getCardStyle();

  if (variant === 'gradient') {
    const Component = onPress ? TouchableOpacity : ThemedView;
    
    return (
      <Component
        onPress={onPress ? handlePress : undefined}
        style={[cardStyle, style]}
        activeOpacity={onPress ? 0.95 : 1}
      >
        <LinearGradient
          colors={Gradients[colorScheme ?? 'light'].surface}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
          style={styles.gradientContainer}
        >
          {children}
        </LinearGradient>
      </Component>
    );
  }

  if (onPress) {
    return (
      <TouchableOpacity
        onPress={handlePress}
        style={[cardStyle, style]}
        activeOpacity={0.95}
      >
        {children}
      </TouchableOpacity>
    );
  }

  return (
    <ThemedView
      type="card"
      style={[cardStyle, style]}
    >
      {children}
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  base: {
    borderRadius: 16,
    overflow: 'hidden',
  },
  none: {
    padding: 0,
  },
  small: {
    padding: 12,
  },
  medium: {
    padding: 16,
  },
  large: {
    padding: 24,
  },
  gradientContainer: {
    flex: 1,
    padding: 16,
    borderRadius: 16,
  },
});

// Specialized card variants
export function VideoCard({ children, ...props }: Omit<CardProps, 'variant'>) {
  return (
    <Card variant="elevated" {...props}>
      {children}
    </Card>
  );
}

export function FeatureCard({ children, ...props }: Omit<CardProps, 'variant'>) {
  return (
    <Card variant="gradient" {...props}>
      {children}
    </Card>
  );
}

export function ProjectCard({ children, ...props }: Omit<CardProps, 'variant'>) {
  return (
    <Card variant="outlined" {...props}>
      {children}
    </Card>
  );
}
