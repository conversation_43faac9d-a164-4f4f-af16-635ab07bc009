import React, { useState, useRef } from 'react';
import { 
  StyleSheet, 
  View, 
  TextInput, 
  TouchableOpacity, 
  ScrollView,
  Platform,
  Keyboard
} from 'react-native';
import * as Haptics from 'expo-haptics';
import { Slider } from '@react-native-community/slider';
import { Ionicons } from '@expo/vector-icons';

import { ThemedText } from '@/components/ThemedText';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { Colors } from '@/constants/Colors';
import { useColorScheme } from '@/hooks/useColorScheme';

// Text overlay types
export interface TextOverlayItem {
  id: string;
  text: string;
  position: { x: number; y: number };
  fontSize: number;
  color: string;
  backgroundColor: string;
  startTime: number;
  endTime: number;
}

interface TextOverlayProps {
  overlays: TextOverlayItem[];
  onAddOverlay: (overlay: TextOverlayItem) => void;
  onUpdateOverlay: (overlay: TextOverlayItem) => void;
  onDeleteOverlay: (overlayId: string) => void;
  videoDuration: number;
}

// Available text colors
const TEXT_COLORS = [
  '#FFFFFF', // White
  '#000000', // Black
  '#FF0000', // Red
  '#00FF00', // Green
  '#0000FF', // Blue
  '#FFFF00', // Yellow
  '#FF00FF', // Magenta
  '#00FFFF', // Cyan
];

// Available background colors (with alpha)
const BG_COLORS = [
  'transparent',
  'rgba(0,0,0,0.5)',
  'rgba(255,255,255,0.5)',
  'rgba(255,0,0,0.5)',
  'rgba(0,255,0,0.5)',
  'rgba(0,0,255,0.5)',
  'rgba(255,255,0,0.5)',
];

export default function TextOverlay({
  overlays,
  onAddOverlay,
  onUpdateOverlay,
  onDeleteOverlay,
  videoDuration
}: TextOverlayProps) {
  const colorScheme = useColorScheme();
  const [text, setText] = useState('');
  const [fontSize, setFontSize] = useState(24);
  const [textColor, setTextColor] = useState(TEXT_COLORS[0]);
  const [bgColor, setBgColor] = useState(BG_COLORS[0]);
  const [startTime, setStartTime] = useState(0);
  const [endTime, setEndTime] = useState(videoDuration);
  const [editingOverlayId, setEditingOverlayId] = useState<string | null>(null);
  
  const textInputRef = useRef<TextInput>(null);
  
  const handleAddOverlay = () => {
    if (!text.trim()) return;
    
    if (editingOverlayId) {
      // Update existing overlay
      const updatedOverlay: TextOverlayItem = {
        id: editingOverlayId,
        text,
        position: { x: 0.5, y: 0.5 }, // Center by default
        fontSize,
        color: textColor,
        backgroundColor: bgColor,
        startTime,
        endTime,
      };
      
      onUpdateOverlay(updatedOverlay);
    } else {
      // Add new overlay
      const newOverlay: TextOverlayItem = {
        id: Date.now().toString(),
        text,
        position: { x: 0.5, y: 0.5 }, // Center by default
        fontSize,
        color: textColor,
        backgroundColor: bgColor,
        startTime,
        endTime,
      };
      
      onAddOverlay(newOverlay);
    }
    
    // Reset form
    resetForm();
    Keyboard.dismiss();
    
    if (Platform.OS !== 'web') {
      Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
    }
  };
  
  const handleEditOverlay = (overlay: TextOverlayItem) => {
    setText(overlay.text);
    setFontSize(overlay.fontSize);
    setTextColor(overlay.color);
    setBgColor(overlay.backgroundColor);
    setStartTime(overlay.startTime);
    setEndTime(overlay.endTime);
    setEditingOverlayId(overlay.id);
    
    // Focus text input
    textInputRef.current?.focus();
    
    if (Platform.OS !== 'web') {
      Haptics.selectionAsync();
    }
  };
  
  const handleDeleteOverlay = (overlayId: string) => {
    onDeleteOverlay(overlayId);
    
    if (editingOverlayId === overlayId) {
      resetForm();
    }
    
    if (Platform.OS !== 'web') {
      Haptics.notificationAsync(Haptics.NotificationFeedbackType.Warning);
    }
  };
  
  const resetForm = () => {
    setText('');
    setFontSize(24);
    setTextColor(TEXT_COLORS[0]);
    setBgColor(BG_COLORS[0]);
    setStartTime(0);
    setEndTime(videoDuration);
    setEditingOverlayId(null);
  };
  
  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs < 10 ? '0' : ''}${secs}`;
  };
  
  return (
    <View style={styles.container}>
      <View style={styles.inputContainer}>
        <TextInput
          ref={textInputRef}
          style={[
            styles.textInput,
            { color: textColor, backgroundColor: bgColor !== 'transparent' ? bgColor : 'rgba(0,0,0,0.1)' }
          ]}
          value={text}
          onChangeText={setText}
          placeholder="Enter text..."
          placeholderTextColor="rgba(0,0,0,0.3)"
          multiline
        />
        
        <View style={styles.controlsRow}>
          <ThemedText style={styles.controlLabel}>Font Size: {fontSize}px</ThemedText>
          <Slider
            style={styles.slider}
            minimumValue={12}
            maximumValue={48}
            step={1}
            value={fontSize}
            onValueChange={setFontSize}
            minimumTrackTintColor={Colors[colorScheme ?? 'light'].tint}
            maximumTrackTintColor="rgba(0,0,0,0.1)"
            thumbTintColor={Colors[colorScheme ?? 'light'].tint}
          />
        </View>
        
        <View style={styles.controlsRow}>
          <ThemedText style={styles.controlLabel}>Text Color:</ThemedText>
          <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.colorPicker}>
            {TEXT_COLORS.map((color) => (
              <TouchableOpacity
                key={color}
                style={[
                  styles.colorOption,
                  { backgroundColor: color },
                  textColor === color && styles.selectedColorOption,
                ]}
                onPress={() => setTextColor(color)}
              />
            ))}
          </ScrollView>
        </View>
        
        <View style={styles.controlsRow}>
          <ThemedText style={styles.controlLabel}>Background:</ThemedText>
          <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.colorPicker}>
            {BG_COLORS.map((color) => (
              <TouchableOpacity
                key={color}
                style={[
                  styles.colorOption,
                  { backgroundColor: color },
                  bgColor === color && styles.selectedColorOption,
                ]}
                onPress={() => setBgColor(color)}
              />
            ))}
          </ScrollView>
        </View>
        
        <View style={styles.controlsRow}>
          <ThemedText style={styles.controlLabel}>
            Time: {formatTime(startTime)} - {formatTime(endTime)}
          </ThemedText>
          <View style={styles.timeControls}>
            <TouchableOpacity 
              style={styles.timeButton}
              onPress={() => setStartTime(0)}
            >
              <ThemedText>Start</ThemedText>
            </TouchableOpacity>
            <TouchableOpacity 
              style={styles.timeButton}
              onPress={() => setEndTime(videoDuration)}
            >
              <ThemedText>End</ThemedText>
            </TouchableOpacity>
          </View>
        </View>
        
        <View style={styles.buttonRow}>
          <TouchableOpacity 
            style={styles.cancelButton}
            onPress={resetForm}
          >
            <ThemedText style={styles.buttonText}>Cancel</ThemedText>
          </TouchableOpacity>
          
          <TouchableOpacity 
            style={[
              styles.addButton,
              !text.trim() && styles.disabledButton,
              { backgroundColor: Colors[colorScheme ?? 'light'].tint }
            ]}
            onPress={handleAddOverlay}
            disabled={!text.trim()}
          >
            <ThemedText style={styles.addButtonText}>
              {editingOverlayId ? 'Update' : 'Add'} Text
            </ThemedText>
          </TouchableOpacity>
        </View>
      </View>
      
      <View style={styles.overlaysList}>
        <ThemedText style={styles.overlaysTitle}>
          Text Overlays ({overlays.length})
        </ThemedText>
        
        {overlays.length === 0 ? (
          <View style={styles.emptyOverlays}>
            <IconSymbol 
              name="text.bubble" 
              size={24} 
              color={Colors[colorScheme ?? 'light'].text} 
              style={{ opacity: 0.5 }}
            />
            <ThemedText style={styles.emptyText}>No text overlays added</ThemedText>
          </View>
        ) : (
          <ScrollView style={styles.overlaysScroll}>
            {overlays.map((overlay) => (
              <View key={overlay.id} style={styles.overlayItem}>
                <View 
                  style={[
                    styles.overlayPreview,
                    { backgroundColor: overlay.backgroundColor }
                  ]}
                >
                  <ThemedText 
                    style={[
                      styles.overlayPreviewText,
                      { 
                        color: overlay.color,
                        fontSize: Math.min(overlay.fontSize, 18)
                      }
                    ]}
                    numberOfLines={1}
                  >
                    {overlay.text}
                  </ThemedText>
                </View>
                
                <View style={styles.overlayInfo}>
                  <ThemedText style={styles.overlayTime}>
                    {formatTime(overlay.startTime)} - {formatTime(overlay.endTime)}
                  </ThemedText>
                </View>
                
                <View style={styles.overlayActions}>
                  <TouchableOpacity
                    style={styles.overlayActionButton}
                    onPress={() => handleEditOverlay(overlay)}
                  >
                    <Ionicons name="pencil" size={18} color={Colors[colorScheme ?? 'light'].text} />
                  </TouchableOpacity>
                  
                  <TouchableOpacity
                    style={styles.overlayActionButton}
                    onPress={() => handleDeleteOverlay(overlay.id)}
                  >
                    <Ionicons name="trash" size={18} color={Colors[colorScheme ?? 'light'].videoEditorPrimary} />
                  </TouchableOpacity>
                </View>
              </View>
            ))}
          </ScrollView>
        )}
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  inputContainer: {
    padding: 10,
    backgroundColor: 'rgba(0,0,0,0.05)',
    borderRadius: 8,
    marginBottom: 20,
  },
  textInput: {
    minHeight: 60,
    borderRadius: 8,
    padding: 10,
    marginBottom: 10,
    textAlign: 'center',
    fontWeight: 'bold',
  },
  controlsRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 10,
  },
  controlLabel: {
    width: 100,
    fontSize: 12,
  },
  slider: {
    flex: 1,
    height: 40,
  },
  colorPicker: {
    flex: 1,
    flexDirection: 'row',
  },
  colorOption: {
    width: 24,
    height: 24,
    borderRadius: 12,
    marginRight: 8,
    borderWidth: 1,
    borderColor: 'rgba(0,0,0,0.1)',
  },
  selectedColorOption: {
    borderWidth: 2,
    borderColor: Colors.light.tint,
  },
  timeControls: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  timeButton: {
    paddingHorizontal: 10,
    paddingVertical: 5,
    backgroundColor: 'rgba(0,0,0,0.1)',
    borderRadius: 4,
  },
  buttonRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 10,
  },
  cancelButton: {
    flex: 1,
    padding: 10,
    backgroundColor: 'rgba(0,0,0,0.1)',
    borderRadius: 8,
    marginRight: 10,
    alignItems: 'center',
  },
  addButton: {
    flex: 2,
    padding: 10,
    borderRadius: 8,
    alignItems: 'center',
  },
  disabledButton: {
    opacity: 0.5,
  },
  buttonText: {
    fontWeight: 'bold',
  },
  addButtonText: {
    color: '#FFFFFF',
    fontWeight: 'bold',
  },
  overlaysList: {
    flex: 1,
  },
  overlaysTitle: {
    fontWeight: 'bold',
    marginBottom: 10,
  },
  emptyOverlays: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0,0,0,0.05)',
    borderRadius: 8,
    padding: 20,
  },
  emptyText: {
    marginTop: 10,
    opacity: 0.5,
  },
  overlaysScroll: {
    flex: 1,
  },
  overlayItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 10,
    backgroundColor: 'rgba(0,0,0,0.05)',
    borderRadius: 8,
    marginBottom: 8,
  },
  overlayPreview: {
    width: 100,
    padding: 5,
    borderRadius: 4,
    justifyContent: 'center',
    alignItems: 'center',
  },
  overlayPreviewText: {
    fontWeight: 'bold',
  },
  overlayInfo: {
    flex: 1,
    marginLeft: 10,
  },
  overlayTime: {
    fontSize: 12,
    opacity: 0.7,
  },
  overlayActions: {
    flexDirection: 'row',
  },
  overlayActionButton: {
    padding: 8,
  },
});
