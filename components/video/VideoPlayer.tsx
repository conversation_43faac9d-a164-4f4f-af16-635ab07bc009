import Slider from '@react-native-community/slider';
import { AVPlaybackStatus, ResizeMode, Video } from 'expo-av';
import * as Haptics from 'expo-haptics';
import { LinearGradient } from 'expo-linear-gradient';
import React, { useCallback, useEffect, useRef, useState } from 'react';
import { ActivityIndicator, Platform, StyleSheet, TouchableOpacity, View } from 'react-native';

import { ThemedText } from '@/components/ThemedText';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { Colors } from '@/constants/Colors';
import { useColorScheme } from '@/hooks/useColorScheme';

interface VideoPlayerProps {
  uri: string;
  autoPlay?: boolean;
  loop?: boolean;
  showControls?: boolean;
  resizeMode?: ResizeMode;
  style?: any;
  onLoad?: () => void;
  onError?: (error: string) => void;
  onProgress?: (progress: number) => void;
  onEnd?: () => void;
  onPlaybackStatusUpdate?: (status: AVPlaybackStatus) => void;
  onSeek?: (position: number) => void;
  initialPosition?: number;
}

export default function VideoPlayer({
  uri,
  autoPlay = false,
  loop = true,
  showControls = true,
  resizeMode = ResizeMode.CONTAIN,
  style = {},
  onLoad,
  onError,
  onProgress,
  onEnd,
  onPlaybackStatusUpdate: externalStatusUpdate,
  onSeek,
  initialPosition = 0,
}: VideoPlayerProps) {
  const colorScheme = useColorScheme();
  const videoRef = useRef<Video>(null);

  const [status, setStatus] = useState<AVPlaybackStatus | null>(null);
  const [loading, setLoading] = useState(true);
  const [currentTime, setCurrentTime] = useState(initialPosition);
  const [duration, setDuration] = useState(0);
  const [isSeeking, setIsSeeking] = useState(false);
  const [controlsVisible, setControlsVisible] = useState(true);
  const [controlsTimeout, setControlsTimeout] = useState<NodeJS.Timeout | null>(null);
  const [error, setError] = useState<string | null>(null);

  // Set initial position when component mounts
  useEffect(() => {
    if (initialPosition > 0 && videoRef.current && status?.isLoaded) {
      videoRef.current.setPositionAsync(initialPosition * 1000);
    }
  }, [initialPosition, status?.isLoaded]);

  // Handle autoplay
  useEffect(() => {
    if (autoPlay && videoRef.current && status?.isLoaded && !status.isPlaying) {
      videoRef.current.playAsync();
    }
  }, [autoPlay, status]);

  // Handle controls visibility
  useEffect(() => {
    // Hide controls after 3 seconds of inactivity
    if (showControls && controlsVisible) {
      if (controlsTimeout) {
        clearTimeout(controlsTimeout);
      }

      const timeout = setTimeout(() => {
        if (status?.isLoaded && status.isPlaying) {
          setControlsVisible(false);
        }
      }, 3000);

      setControlsTimeout(timeout);
    }

    return () => {
      if (controlsTimeout) {
        clearTimeout(controlsTimeout);
      }
    };
  }, [controlsVisible, showControls, status]);

  const onPlaybackStatusUpdate = useCallback((status: AVPlaybackStatus) => {
    setStatus(status);

    if (status.isLoaded) {
      if (!duration && status.durationMillis) {
        setDuration(status.durationMillis / 1000);
      }

      if (!isSeeking && status.positionMillis) {
        setCurrentTime(status.positionMillis / 1000);
        onProgress?.(status.positionMillis / (status.durationMillis || 1));
      }

      if (status.didJustFinish) {
        onEnd?.();
      }
    } else if (status.error) {
      setError(status.error);
      onError?.(status.error);
    }

    // Pass status to external handler if provided
    externalStatusUpdate?.(status);
  }, [duration, isSeeking, onProgress, onEnd, externalStatusUpdate, onError]);

  const handlePlayPause = useCallback(() => {
    if (videoRef.current) {
      if (status?.isLoaded && status.isPlaying) {
        videoRef.current.pauseAsync();
      } else {
        videoRef.current.playAsync();
      }

      // Provide haptic feedback
      if (Platform.OS !== 'web') {
        Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
      }
    }
  }, [status]);

  const handleVideoPress = useCallback(() => {
    setControlsVisible(prev => !prev);
  }, []);

  const formatTime = useCallback((seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs < 10 ? '0' : ''}${secs}`;
  }, []);

  const handleSliderValueChange = useCallback((value: number) => {
    setIsSeeking(true);
    setCurrentTime(value);
  }, []);

  const handleSliderSlidingComplete = useCallback((value: number) => {
    setIsSeeking(false);
    if (videoRef.current) {
      videoRef.current.setPositionAsync(value * 1000);
      onSeek?.(value);

      // Provide haptic feedback
      if (Platform.OS !== 'web') {
        Haptics.selectionAsync();
      }
    }
  }, [onSeek]);

  // Render error state
  if (error) {
    return (
      <View style={[styles.container, style]}>
        <View style={styles.errorContainer}>
          <IconSymbol name="exclamationmark.triangle" size={40} color={Colors[colorScheme ?? 'light'].videoEditorPrimary} />
          <ThemedText style={styles.errorText}>Failed to load video</ThemedText>
          <TouchableOpacity
            style={styles.retryButton}
            onPress={() => {
              setError(null);
              setLoading(true);
              if (videoRef.current) {
                videoRef.current.loadAsync({ uri }, {}, false);
              }
            }}
          >
            <ThemedText style={styles.retryButtonText}>Retry</ThemedText>
          </TouchableOpacity>
        </View>
      </View>
    );
  }

  return (
    <View style={[styles.container, style]}>
      <TouchableOpacity
        style={styles.videoWrapper}
        activeOpacity={1}
        onPress={handleVideoPress}
      >
        <Video
          ref={videoRef}
          style={styles.video}
          source={{ uri }}
          resizeMode={resizeMode}
          isLooping={loop}
          onPlaybackStatusUpdate={onPlaybackStatusUpdate}
          onLoad={() => {
            setLoading(false);
            onLoad?.();
          }}
          onError={(error) => {
            console.error('Video error:', error);
            setError(error.error);
            onError?.(error.error);
          }}
          progressUpdateIntervalMillis={500}
        />

        {loading && (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color={Colors[colorScheme ?? 'light'].tint} />
          </View>
        )}

        {showControls && controlsVisible && (
          <>
            <LinearGradient
              colors={['rgba(0,0,0,0.7)', 'transparent']}
              style={styles.videoOverlayTop}
            />

            <LinearGradient
              colors={['transparent', 'rgba(0,0,0,0.7)']}
              style={styles.videoOverlayBottom}
            >
              <TouchableOpacity
                style={styles.playButton}
                onPress={handlePlayPause}
              >
                <IconSymbol
                  name={status?.isLoaded && status.isPlaying ? "pause.fill" : "play.fill"}
                  size={24}
                  color="#FFFFFF"
                />
              </TouchableOpacity>

              <View style={styles.timelineContainer}>
                <ThemedText style={styles.timeText}>{formatTime(currentTime)}</ThemedText>
                <Slider
                  style={styles.slider}
                  minimumValue={0}
                  maximumValue={duration || 1}
                  value={currentTime}
                  minimumTrackTintColor={Colors[colorScheme ?? 'light'].tint}
                  maximumTrackTintColor="rgba(255,255,255,0.3)"
                  thumbTintColor={Colors[colorScheme ?? 'light'].tint}
                  onValueChange={handleSliderValueChange}
                  onSlidingComplete={handleSliderSlidingComplete}
                />
                <ThemedText style={styles.timeText}>{formatTime(duration)}</ThemedText>
              </View>
            </LinearGradient>
          </>
        )}
      </TouchableOpacity>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  videoWrapper: {
    flex: 1,
    backgroundColor: '#000',
    position: 'relative',
  },
  video: {
    flex: 1,
  },
  loadingContainer: {
    ...StyleSheet.absoluteFillObject,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0,0,0,0.3)',
  },
  errorContainer: {
    ...StyleSheet.absoluteFillObject,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#000',
    padding: 20,
  },
  errorText: {
    color: '#FFFFFF',
    marginTop: 10,
    marginBottom: 20,
    textAlign: 'center',
  },
  retryButton: {
    backgroundColor: Colors.light.videoEditorPrimary,
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 8,
  },
  retryButtonText: {
    color: '#FFFFFF',
    fontWeight: 'bold',
  },
  videoOverlayTop: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    height: 60,
  },
  videoOverlayBottom: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    height: 80,
    justifyContent: 'center',
    alignItems: 'center',
  },
  playButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(0,0,0,0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 10,
  },
  timelineContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    width: '100%',
    paddingHorizontal: 20,
  },
  slider: {
    flex: 1,
    height: 40,
  },
  timeText: {
    color: '#FFFFFF',
    fontSize: 12,
    width: 40,
    textAlign: 'center',
  },
});
