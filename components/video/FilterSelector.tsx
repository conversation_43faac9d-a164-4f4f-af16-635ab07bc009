import React, { useState, useEffect } from 'react';
import { 
  StyleSheet, 
  View, 
  ScrollView, 
  TouchableOpacity, 
  Image, 
  ActivityIndicator,
  Platform
} from 'react-native';
import * as Haptics from 'expo-haptics';

import { ThemedText } from '@/components/ThemedText';
import { Colors } from '@/constants/Colors';
import { useColorScheme } from '@/hooks/useColorScheme';
import { FFmpegProcessor } from '@/services/video/FFmpegProcessor';

// Define filter types
export interface VideoFilter {
  id: string;
  name: string;
  filter: string;
  thumbnailUri?: string;
}

interface FilterSelectorProps {
  videoUri: string;
  filters: VideoFilter[];
  selectedFilterId: string;
  onFilterSelect: (filterId: string) => void;
}

export default function FilterSelector({
  videoUri,
  filters,
  selectedFilterId,
  onFilterSelect
}: FilterSelectorProps) {
  const colorScheme = useColorScheme();
  const [filterThumbnails, setFilterThumbnails] = useState<Record<string, string>>({});
  const [loadingThumbnails, setLoadingThumbnails] = useState(false);
  
  // Generate filter thumbnails
  useEffect(() => {
    const generateFilterThumbnails = async () => {
      if (!videoUri) return;
      
      const filtersWithoutThumbnails = filters.filter(
        filter => !filter.thumbnailUri && !filterThumbnails[filter.id]
      );
      
      if (filtersWithoutThumbnails.length === 0) return;
      
      setLoadingThumbnails(true);
      
      try {
        const ffmpegProcessor = new FFmpegProcessor();
        await ffmpegProcessor.load();
        
        const newThumbnails: Record<string, string> = { ...filterThumbnails };
        
        // First generate a base thumbnail
        const baseThumbnail = await ffmpegProcessor.generateThumbnail(videoUri, {
          time: 1, // 1 second into the video
          width: 120,
          height: 120,
          quality: 80,
        });
        
        // Generate thumbnails for each filter
        for (const filter of filtersWithoutThumbnails) {
          try {
            if (filter.id === 'original') {
              // Use base thumbnail for original
              newThumbnails[filter.id] = baseThumbnail;
            } else {
              // Apply filter to base thumbnail
              const filteredThumbnail = await ffmpegProcessor.applyFilter(
                baseThumbnail,
                filter.filter
              );
              
              newThumbnails[filter.id] = filteredThumbnail;
            }
          } catch (error) {
            console.error(`Error generating thumbnail for filter ${filter.id}:`, error);
            // Use base thumbnail as fallback
            newThumbnails[filter.id] = baseThumbnail;
          }
        }
        
        setFilterThumbnails(newThumbnails);
      } catch (error) {
        console.error('Error generating filter thumbnails:', error);
      } finally {
        setLoadingThumbnails(false);
      }
    };
    
    generateFilterThumbnails();
  }, [videoUri, filters]);
  
  const handleFilterPress = (filterId: string) => {
    onFilterSelect(filterId);
    
    if (Platform.OS !== 'web') {
      Haptics.selectionAsync();
    }
  };
  
  return (
    <View style={styles.container}>
      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={styles.scrollContent}
      >
        {loadingThumbnails && (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="small" color={Colors[colorScheme ?? 'light'].tint} />
            <ThemedText style={styles.loadingText}>Generating previews...</ThemedText>
          </View>
        )}
        
        {filters.map((filter) => {
          const isSelected = filter.id === selectedFilterId;
          const thumbnailUri = filter.thumbnailUri || filterThumbnails[filter.id];
          
          return (
            <TouchableOpacity
              key={filter.id}
              style={[
                styles.filterItem,
                isSelected && styles.selectedFilterItem,
              ]}
              onPress={() => handleFilterPress(filter.id)}
            >
              {thumbnailUri ? (
                <Image
                  source={{ uri: thumbnailUri }}
                  style={styles.filterThumbnail}
                  resizeMode="cover"
                />
              ) : (
                <View style={styles.placeholderThumbnail}>
                  <ActivityIndicator size="small" color={Colors[colorScheme ?? 'light'].tint} />
                </View>
              )}
              
              <View 
                style={[
                  styles.filterNameContainer,
                  isSelected && { backgroundColor: Colors[colorScheme ?? 'light'].tint }
                ]}
              >
                <ThemedText 
                  style={[
                    styles.filterName,
                    isSelected && styles.selectedFilterName
                  ]}
                >
                  {filter.name}
                </ThemedText>
              </View>
            </TouchableOpacity>
          );
        })}
      </ScrollView>
    </View>
  );
}

// Default filters
export const DEFAULT_VIDEO_FILTERS: VideoFilter[] = [
  { id: 'original', name: 'Original', filter: '' },
  { id: 'warm', name: 'Warm', filter: 'colortemperature=4000K' },
  { id: 'cool', name: 'Cool', filter: 'colortemperature=10000K' },
  { id: 'vintage', name: 'Vintage', filter: 'curves=vintage' },
  { id: 'bw', name: 'B&W', filter: 'hue=s=0' },
  { id: 'vivid', name: 'Vivid', filter: 'eq=saturation=1.5:contrast=1.2' },
  { id: 'sepia', name: 'Sepia', filter: 'colorchannelmixer=.393:.769:.189:0:.349:.686:.168:0:.272:.534:.131' },
  { id: 'sharpen', name: 'Sharpen', filter: 'unsharp=5:5:1.5:5:5:0.0' },
  { id: 'blur', name: 'Blur', filter: 'boxblur=5:1' },
];

const styles = StyleSheet.create({
  container: {
    height: 160,
  },
  scrollContent: {
    paddingHorizontal: 10,
    paddingVertical: 10,
    gap: 12,
  },
  filterItem: {
    width: 100,
    borderRadius: 8,
    overflow: 'hidden',
    backgroundColor: 'rgba(0,0,0,0.05)',
  },
  selectedFilterItem: {
    borderWidth: 2,
    borderColor: Colors.light.tint,
  },
  filterThumbnail: {
    width: '100%',
    height: 100,
  },
  placeholderThumbnail: {
    width: '100%',
    height: 100,
    backgroundColor: 'rgba(0,0,0,0.1)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  filterNameContainer: {
    padding: 6,
    alignItems: 'center',
  },
  filterName: {
    fontSize: 12,
    fontWeight: 'bold',
  },
  selectedFilterName: {
    color: '#FFFFFF',
  },
  loadingContainer: {
    height: 100,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 20,
  },
  loadingText: {
    fontSize: 12,
    marginTop: 8,
  },
});
