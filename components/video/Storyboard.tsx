import React, { useState, useRef, useEffect } from 'react';
import { 
  StyleSheet, 
  View, 
  ScrollView, 
  TouchableOpacity, 
  Image, 
  Text,
  Dimensions,
  ActivityIndicator,
  Platform
} from 'react-native';
import Animated, { 
  useSharedValue, 
  useAnimatedStyle, 
  withTiming,
  withSpring,
  runOnJS
} from 'react-native-reanimated';
import { Gesture, GestureDetector, GestureHandlerRootView } from 'react-native-gesture-handler';
import * as Haptics from 'expo-haptics';

import { ThemedText } from '@/components/ThemedText';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { Colors } from '@/constants/Colors';
import { useColorScheme } from '@/hooks/useColorScheme';
import { FFmpegProcessor } from '@/services/video/FFmpegProcessor';

export interface ClipItem {
  id: string;
  uri: string;
  thumbnailUri?: string;
  startTime: number;
  endTime: number;
  duration: number;
  interestScore: number;
}

interface StoryboardProps {
  clips: ClipItem[];
  onClipPress?: (clipId: string) => void;
  onClipReorder?: (newOrder: ClipItem[]) => void;
  onClipTrimChange?: (clipId: string, startTime: number, endTime: number) => void;
  selectedClipId?: string;
  loading?: boolean;
}

export default function Storyboard({
  clips,
  onClipPress,
  onClipReorder,
  onClipTrimChange,
  selectedClipId,
  loading = false
}: StoryboardProps) {
  const colorScheme = useColorScheme();
  const scrollViewRef = useRef<ScrollView>(null);
  const [thumbnails, setThumbnails] = useState<Record<string, string>>({});
  const [draggingClipId, setDraggingClipId] = useState<string | null>(null);
  const [clipsOrder, setClipsOrder] = useState<ClipItem[]>(clips);
  const [loadingThumbnails, setLoadingThumbnails] = useState(false);
  
  const ffmpegProcessor = useRef(new FFmpegProcessor()).current;
  
  // Update clips order when clips prop changes
  useEffect(() => {
    setClipsOrder(clips);
  }, [clips]);
  
  // Generate thumbnails for clips that don't have them
  useEffect(() => {
    const generateThumbnails = async () => {
      const clipsWithoutThumbnails = clips.filter(clip => !clip.thumbnailUri && !thumbnails[clip.id]);
      
      if (clipsWithoutThumbnails.length === 0) return;
      
      setLoadingThumbnails(true);
      
      try {
        const newThumbnails: Record<string, string> = { ...thumbnails };
        
        for (const clip of clipsWithoutThumbnails) {
          try {
            // Generate thumbnail at 1/3 of the clip duration
            const thumbnailTime = clip.startTime + (clip.duration / 3);
            const thumbnailUri = await ffmpegProcessor.generateThumbnail(clip.uri, {
              time: thumbnailTime,
              width: 160,
              height: 90,
              quality: 80,
            });
            
            newThumbnails[clip.id] = thumbnailUri;
          } catch (error) {
            console.error(`Error generating thumbnail for clip ${clip.id}:`, error);
          }
        }
        
        setThumbnails(newThumbnails);
      } catch (error) {
        console.error('Error generating thumbnails:', error);
      } finally {
        setLoadingThumbnails(false);
      }
    };
    
    generateThumbnails();
  }, [clips]);
  
  const handleLongPress = (clipId: string) => {
    setDraggingClipId(clipId);
    if (Platform.OS !== 'web') {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    }
  };
  
  const handleDragEnd = () => {
    setDraggingClipId(null);
    if (onClipReorder) {
      onClipReorder(clipsOrder);
    }
  };
  
  const handleClipMove = (fromIndex: number, toIndex: number) => {
    if (fromIndex === toIndex) return;
    
    const newOrder = [...clipsOrder];
    const [movedClip] = newOrder.splice(fromIndex, 1);
    newOrder.splice(toIndex, 0, movedClip);
    
    setClipsOrder(newOrder);
    
    if (Platform.OS !== 'web') {
      Haptics.selectionAsync();
    }
  };
  
  const renderClip = (clip: ClipItem, index: number) => {
    const isSelected = clip.id === selectedClipId;
    const isDragging = clip.id === draggingClipId;
    
    // Calculate width based on interest score (min 80px, max 160px)
    const baseWidth = 80;
    const maxExtraWidth = 80;
    const width = baseWidth + (maxExtraWidth * (clip.interestScore / 100));
    
    // Get thumbnail URI
    const thumbnailUri = clip.thumbnailUri || thumbnails[clip.id];
    
    return (
      <Animated.View
        key={clip.id}
        style={[
          styles.clipContainer,
          { width },
          isDragging && styles.draggingClip,
          isSelected && styles.selectedClip,
        ]}
      >
        <TouchableOpacity
          style={styles.clipTouchable}
          onPress={() => onClipPress?.(clip.id)}
          onLongPress={() => handleLongPress(clip.id)}
          delayLongPress={300}
        >
          {thumbnailUri ? (
            <Image
              source={{ uri: thumbnailUri }}
              style={styles.clipThumbnail}
              resizeMode="cover"
            />
          ) : (
            <View style={styles.placeholderThumbnail}>
              <ActivityIndicator size="small" color={Colors[colorScheme ?? 'light'].tint} />
            </View>
          )}
          
          <View style={styles.clipInfo}>
            <Text style={styles.clipDuration}>
              {formatDuration(clip.duration)}
            </Text>
          </View>
          
          {isSelected && (
            <View style={styles.selectedIndicator}>
              <IconSymbol name="checkmark.circle.fill" size={16} color="#FFFFFF" />
            </View>
          )}
        </TouchableOpacity>
      </Animated.View>
    );
  };
  
  const formatDuration = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs < 10 ? '0' : ''}${secs}`;
  };
  
  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={Colors[colorScheme ?? 'light'].tint} />
        <ThemedText style={styles.loadingText}>Analyzing clips...</ThemedText>
      </View>
    );
  }
  
  if (clips.length === 0) {
    return (
      <View style={styles.emptyContainer}>
        <IconSymbol 
          name="film" 
          size={40} 
          color={Colors[colorScheme ?? 'light'].text} 
          style={{ opacity: 0.5 }}
        />
        <ThemedText style={styles.emptyText}>No clips available</ThemedText>
      </View>
    );
  }
  
  return (
    <GestureHandlerRootView style={styles.container}>
      <ScrollView
        ref={scrollViewRef}
        horizontal
        contentContainerStyle={styles.scrollContent}
        showsHorizontalScrollIndicator={false}
      >
        {loadingThumbnails && (
          <View style={styles.thumbnailLoadingIndicator}>
            <ActivityIndicator size="small" color={Colors[colorScheme ?? 'light'].tint} />
          </View>
        )}
        
        {clipsOrder.map((clip, index) => renderClip(clip, index))}
      </ScrollView>
    </GestureHandlerRootView>
  );
}

const styles = StyleSheet.create({
  container: {
    height: 120,
    backgroundColor: 'rgba(0,0,0,0.05)',
    borderRadius: 8,
  },
  scrollContent: {
    paddingHorizontal: 10,
    paddingVertical: 10,
    alignItems: 'center',
    gap: 8,
  },
  clipContainer: {
    height: 90,
    borderRadius: 6,
    overflow: 'hidden',
  },
  clipTouchable: {
    flex: 1,
    position: 'relative',
  },
  clipThumbnail: {
    width: '100%',
    height: '100%',
  },
  placeholderThumbnail: {
    width: '100%',
    height: '100%',
    backgroundColor: 'rgba(0,0,0,0.2)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  clipInfo: {
    position: 'absolute',
    bottom: 4,
    right: 4,
    backgroundColor: 'rgba(0,0,0,0.6)',
    paddingHorizontal: 4,
    paddingVertical: 2,
    borderRadius: 4,
  },
  clipDuration: {
    color: '#FFFFFF',
    fontSize: 10,
  },
  selectedClip: {
    borderWidth: 2,
    borderColor: Colors.light.tint,
  },
  draggingClip: {
    opacity: 0.7,
    transform: [{ scale: 1.05 }],
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 4,
    elevation: 5,
  },
  selectedIndicator: {
    position: 'absolute',
    top: 4,
    right: 4,
    backgroundColor: Colors.light.tint,
    borderRadius: 10,
  },
  loadingContainer: {
    height: 120,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 14,
  },
  emptyContainer: {
    height: 120,
    justifyContent: 'center',
    alignItems: 'center',
  },
  emptyText: {
    marginTop: 10,
    opacity: 0.5,
  },
  thumbnailLoadingIndicator: {
    position: 'absolute',
    top: 10,
    left: 10,
    backgroundColor: 'rgba(0,0,0,0.3)',
    borderRadius: 10,
    width: 20,
    height: 20,
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 10,
  },
});
