import React, { useState, useEffect, useRef } from 'react';
import { 
  StyleSheet, 
  View, 
  PanResponder, 
  Animated, 
  Text,
  Platform,
  LayoutChangeEvent
} from 'react-native';
import * as Haptics from 'expo-haptics';

import { Colors } from '@/constants/Colors';
import { useColorScheme } from '@/hooks/useColorScheme';

interface TrimControlsProps {
  duration: number;
  trimStart: number;
  trimEnd: number;
  onTrimStartChange: (value: number) => void;
  onTrimEndChange: (value: number) => void;
  onScrubbing?: (isScrubbing: boolean) => void;
  minimumTrimDuration?: number;
  thumbnails?: string[];
}

export default function TrimControls({
  duration,
  trimStart,
  trimEnd,
  onTrimStartChange,
  onTrimEndChange,
  onScrubbing,
  minimumTrimDuration = 1,
  thumbnails = []
}: TrimControlsProps) {
  const colorScheme = useColorScheme();
  const [containerWidth, setContainerWidth] = useState(0);
  const [isDraggingStart, setIsDraggingStart] = useState(false);
  const [isDraggingEnd, setIsDraggingEnd] = useState(false);
  
  const startHandlePosition = useRef(new Animated.Value(0)).current;
  const endHandlePosition = useRef(new Animated.Value(0)).current;
  
  // Update handle positions when trim values change
  useEffect(() => {
    if (containerWidth > 0) {
      const startPos = (trimStart / duration) * containerWidth;
      const endPos = (trimEnd / duration) * containerWidth;
      
      startHandlePosition.setValue(startPos);
      endHandlePosition.setValue(endPos);
    }
  }, [trimStart, trimEnd, duration, containerWidth]);
  
  // Pan responder for start handle
  const startPanResponder = useRef(
    PanResponder.create({
      onStartShouldSetPanResponder: () => true,
      onPanResponderGrant: () => {
        setIsDraggingStart(true);
        onScrubbing?.(true);
        if (Platform.OS !== 'web') {
          Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
        }
      },
      onPanResponderMove: (_, gestureState) => {
        const newPosition = Math.max(0, Math.min(
          (trimEnd - minimumTrimDuration) * containerWidth / duration,
          gestureState.moveX
        ));
        
        startHandlePosition.setValue(newPosition);
        
        // Calculate new trim start time
        const newTrimStart = (newPosition / containerWidth) * duration;
        onTrimStartChange(newTrimStart);
      },
      onPanResponderRelease: () => {
        setIsDraggingStart(false);
        onScrubbing?.(false);
        if (Platform.OS !== 'web') {
          Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
        }
      }
    })
  ).current;
  
  // Pan responder for end handle
  const endPanResponder = useRef(
    PanResponder.create({
      onStartShouldSetPanResponder: () => true,
      onPanResponderGrant: () => {
        setIsDraggingEnd(true);
        onScrubbing?.(true);
        if (Platform.OS !== 'web') {
          Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
        }
      },
      onPanResponderMove: (_, gestureState) => {
        const newPosition = Math.min(containerWidth, Math.max(
          (trimStart + minimumTrimDuration) * containerWidth / duration,
          gestureState.moveX
        ));
        
        endHandlePosition.setValue(newPosition);
        
        // Calculate new trim end time
        const newTrimEnd = (newPosition / containerWidth) * duration;
        onTrimEndChange(newTrimEnd);
      },
      onPanResponderRelease: () => {
        setIsDraggingEnd(false);
        onScrubbing?.(false);
        if (Platform.OS !== 'web') {
          Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
        }
      }
    })
  ).current;
  
  const handleLayout = (event: LayoutChangeEvent) => {
    const { width } = event.nativeEvent.layout;
    setContainerWidth(width);
    
    // Initialize handle positions
    const startPos = (trimStart / duration) * width;
    const endPos = (trimEnd / duration) * width;
    
    startHandlePosition.setValue(startPos);
    endHandlePosition.setValue(endPos);
  };
  
  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs < 10 ? '0' : ''}${secs}`;
  };
  
  // Calculate selection width and position
  const selectionWidth = Animated.subtract(endHandlePosition, startHandlePosition);
  
  return (
    <View style={styles.container}>
      <View style={styles.timeLabels}>
        <Text style={styles.timeLabel}>{formatTime(trimStart)}</Text>
        <Text style={styles.timeLabel}>{formatTime(trimEnd)}</Text>
      </View>
      
      <View style={styles.trimContainer} onLayout={handleLayout}>
        {/* Timeline background */}
        <View style={styles.timeline}>
          {thumbnails.length > 0 ? (
            thumbnails.map((uri, index) => (
              <View 
                key={index} 
                style={[
                  styles.thumbnailContainer, 
                  { width: containerWidth / thumbnails.length }
                ]}
              >
                <View style={styles.thumbnail} />
              </View>
            ))
          ) : (
            <View style={styles.emptyTimeline} />
          )}
        </View>
        
        {/* Selection area */}
        <Animated.View
          style={[
            styles.selection,
            {
              left: startHandlePosition,
              width: selectionWidth,
              backgroundColor: Colors[colorScheme ?? 'light'].tint + '40', // 25% opacity
              borderColor: Colors[colorScheme ?? 'light'].tint,
            },
          ]}
        />
        
        {/* Start handle */}
        <Animated.View
          style={[
            styles.handle,
            styles.startHandle,
            { left: startHandlePosition },
            isDraggingStart && styles.activeHandle,
          ]}
          {...startPanResponder.panHandlers}
        >
          <View 
            style={[
              styles.handleBar, 
              { backgroundColor: Colors[colorScheme ?? 'light'].tint }
            ]} 
          />
        </Animated.View>
        
        {/* End handle */}
        <Animated.View
          style={[
            styles.handle,
            styles.endHandle,
            { left: endHandlePosition },
            isDraggingEnd && styles.activeHandle,
          ]}
          {...endPanResponder.panHandlers}
        >
          <View 
            style={[
              styles.handleBar, 
              { backgroundColor: Colors[colorScheme ?? 'light'].tint }
            ]} 
          />
        </Animated.View>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    width: '100%',
    marginVertical: 10,
  },
  timeLabels: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 5,
  },
  timeLabel: {
    fontSize: 12,
    color: '#666',
  },
  trimContainer: {
    height: 40,
    position: 'relative',
  },
  timeline: {
    height: 30,
    backgroundColor: '#E0E0E0',
    borderRadius: 4,
    overflow: 'hidden',
    flexDirection: 'row',
  },
  emptyTimeline: {
    flex: 1,
    backgroundColor: '#E0E0E0',
  },
  thumbnailContainer: {
    height: '100%',
  },
  thumbnail: {
    flex: 1,
    backgroundColor: '#CCCCCC',
    marginHorizontal: 1,
  },
  selection: {
    position: 'absolute',
    height: 30,
    borderWidth: 2,
    borderRadius: 4,
    top: 0,
  },
  handle: {
    position: 'absolute',
    width: 20,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 10,
  },
  startHandle: {
    marginLeft: -10,
  },
  endHandle: {
    marginLeft: -10,
  },
  handleBar: {
    width: 4,
    height: 36,
    borderRadius: 2,
  },
  activeHandle: {
    transform: [{ scale: 1.1 }],
  },
});
