import { StyleSheet, View, type ViewProps } from 'react-native';

import { Shadows } from '@/constants/Colors';
import { useThemeColor } from '@/hooks/useThemeColor';

export type ThemedViewProps = ViewProps & {
  lightColor?: string;
  darkColor?: string;
  type?: 'default' | 'card' | 'surface' | 'elevated' | 'transparent';
  shadow?: 'none' | 'small' | 'medium' | 'large';
};

export function ThemedView({
  style,
  lightColor,
  darkColor,
  type = 'default',
  shadow = 'none',
  ...otherProps
}: ThemedViewProps) {
  const getBackgroundColor = () => {
    switch (type) {
      case 'card':
        return useThemeColor({ light: lightColor, dark: darkColor }, 'cardBackground');
      case 'surface':
        return useThemeColor({ light: lightColor, dark: darkColor }, 'surfaceElevated');
      case 'elevated':
        return useThemeColor({ light: lightColor, dark: darkColor }, 'surfaceElevated');
      case 'transparent':
        return 'transparent';
      default:
        return useThemeColor({ light: lightColor, dark: darkColor }, 'background');
    }
  };

  const backgroundColor = getBackgroundColor();
  const shadowStyle = shadow !== 'none' ? Shadows[shadow] : {};

  return (
    <View
      style={[
        { backgroundColor },
        shadowStyle,
        type === 'card' && styles.card,
        type === 'surface' && styles.surface,
        type === 'elevated' && styles.elevated,
        style
      ]}
      {...otherProps}
    />
  );
}

const styles = StyleSheet.create({
  card: {
    borderRadius: 12,
    padding: 16,
  },
  surface: {
    borderRadius: 8,
  },
  elevated: {
    borderRadius: 12,
    padding: 20,
  },
});
