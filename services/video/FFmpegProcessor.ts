import { FFmpeg } from '@ffmpeg/ffmpeg';
import { fetchFile } from '@ffmpeg/util';
import * as FileSystem from 'expo-file-system';
import { Platform } from 'react-native';

// Interface for video metadata
export interface VideoMetadata {
  duration: number;
  width: number;
  height: number;
  fps: number;
  bitrate: number;
  codec: string;
}

// Interface for thumbnail options
export interface ThumbnailOptions {
  time?: number;
  width?: number;
  height?: number;
  quality?: number;
}

// Interface for export options
export interface ExportOptions {
  width?: number;
  height?: number;
  bitrate?: string;
  fps?: number;
  codec?: string;
  filter?: string;
  startTime?: number;
  endTime?: number;
}

// Class for FFmpeg video processing
export class FFmpegProcessor {
  private ffmpeg: FFmpeg | null = null;
  private isLoaded: boolean = false;
  private isLoading: boolean = false;
  private loadPromise: Promise<void> | null = null;
  private processingQueue: Promise<any> = Promise.resolve();

  constructor() {
    // Pre-create cache directories
    this.createCacheDirectories();
  }

  // Create cache directories for temporary files
  private async createCacheDirectories(): Promise<void> {
    try {
      const dirs = [
        FileSystem.cacheDirectory + 'thumbnails/',
        FileSystem.cacheDirectory + 'videos/',
        FileSystem.cacheDirectory + 'audio/',
      ];

      for (const dir of dirs) {
        await FileSystem.makeDirectoryAsync(dir, { intermediates: true }).catch(() => {});
      }
    } catch (error) {
      console.warn('Error creating cache directories:', error);
    }
  }

  // Load FFmpeg
  public async load(): Promise<void> {
    if (this.isLoaded) {
      return;
    }

    if (this.isLoading && this.loadPromise) {
      return this.loadPromise;
    }

    this.isLoading = true;
    this.loadPromise = this.loadFFmpeg();
    return this.loadPromise;
  }

  // Load FFmpeg implementation
  private async loadFFmpeg(): Promise<void> {
    try {
      // Check if platform is supported
      if (Platform.OS === 'web') {
        throw new Error('FFmpeg is not supported on web platform');
      }

      this.ffmpeg = new FFmpeg();

      // Log progress
      this.ffmpeg.on('log', ({ message }) => {
        console.log('FFmpeg log:', message);
      });

      // Progress tracking
      this.ffmpeg.on('progress', ({ progress }) => {
        console.log(`FFmpeg progress: ${Math.round(progress * 100)}%`);
      });

      // Load FFmpeg
      await this.ffmpeg.load();

      this.isLoaded = true;
      this.isLoading = false;
      console.log('FFmpeg loaded successfully');
    } catch (error) {
      this.isLoading = false;
      console.error('Failed to load FFmpeg:', error);
      throw new Error('Failed to load FFmpeg');
    }
  }

  // Queue processing tasks to prevent concurrent FFmpeg operations
  private async queueTask<T>(task: () => Promise<T>): Promise<T> {
    // Chain the new task to the end of the queue
    this.processingQueue = this.processingQueue
      .then(() => task())
      .catch(error => {
        console.error('Error in FFmpeg task queue:', error);
        throw error;
      });

    return this.processingQueue as Promise<T>;
  }

  // Get video metadata
  public async getMetadata(videoUri: string): Promise<VideoMetadata> {
    return this.queueTask(async () => {
      await this.load();

      if (!this.ffmpeg) {
        throw new Error('FFmpeg not loaded');
      }

      try {
        // Write the input file to FFmpeg's virtual file system
        await this.ffmpeg.writeFile('input.mp4', await fetchFile(videoUri));

        // Run FFmpeg to get metadata
        await this.ffmpeg.exec([
          '-i', 'input.mp4',
          '-v', 'quiet',
          '-print_format', 'json',
          '-show_format',
          '-show_streams',
          '-hide_banner',
          'metadata.json'
        ]);

        // Read the metadata
        const metadataData = await this.ffmpeg.readFile('metadata.json');
        const metadataText = new TextDecoder().decode(metadataData);
        const metadata = JSON.parse(metadataText);

        // Extract relevant information
        const videoStream = metadata.streams.find((stream: any) => stream.codec_type === 'video');
        const format = metadata.format;

        if (!videoStream || !format) {
          throw new Error('Invalid video format or missing video stream');
        }

        // Parse frame rate (handle both fraction and decimal formats)
        let fps = 0;
        if (videoStream.r_frame_rate) {
          const fpsMatch = videoStream.r_frame_rate.match(/(\d+)\/(\d+)/);
          if (fpsMatch) {
            fps = parseInt(fpsMatch[1]) / parseInt(fpsMatch[2]);
          } else {
            fps = parseFloat(videoStream.r_frame_rate);
          }
        }

        return {
          duration: parseFloat(format.duration || '0'),
          width: videoStream.width || 0,
          height: videoStream.height || 0,
          fps: fps,
          bitrate: parseInt(format.bit_rate || '0'),
          codec: videoStream.codec_name || '',
        };
      } catch (error) {
        console.error('Error getting video metadata:', error);

        // Return default metadata if there's an error
        return {
          duration: 0,
          width: 0,
          height: 0,
          fps: 0,
          bitrate: 0,
          codec: '',
        };
      } finally {
        // Clean up
        try {
          await this.ffmpeg?.deleteFile('input.mp4');
          await this.ffmpeg?.deleteFile('metadata.json');
        } catch (e) {
          // Ignore cleanup errors
        }
      }
    });
  }

  // Generate thumbnail from video
  public async generateThumbnail(
    videoUri: string,
    options: ThumbnailOptions = {}
  ): Promise<string> {
    return this.queueTask(async () => {
      await this.load();

      if (!this.ffmpeg) {
        throw new Error('FFmpeg not loaded');
      }

      try {
        // Set default options
        const time = options.time !== undefined ? options.time : 0;
        const width = options.width || 320;
        const height = options.height || 180;
        const quality = options.quality || 90;

        // Generate a unique filename to avoid conflicts
        const inputFileName = `input_${Date.now()}_${Math.floor(Math.random() * 10000)}.mp4`;
        const outputFileName = `thumbnail_${Date.now()}_${Math.floor(Math.random() * 10000)}.jpg`;

        // Write the input file to FFmpeg's virtual file system
        await this.ffmpeg.writeFile(inputFileName, await fetchFile(videoUri));

        // Run FFmpeg to generate thumbnail
        await this.ffmpeg.exec([
          '-ss', time.toString(),
          '-i', inputFileName,
          '-vframes', '1',
          '-vf', `scale=${width}:${height}`,
          '-q:v', (Math.floor(31 * (1 - quality / 100))).toString(),
          outputFileName
        ]);

        // Read the thumbnail
        const thumbnailData = await this.ffmpeg.readFile(outputFileName);

        // Save the thumbnail to a temporary file
        const tempDir = FileSystem.cacheDirectory + 'thumbnails/';

        const thumbnailUri = tempDir + `thumbnail_${Date.now()}_${Math.floor(Math.random() * 10000)}.jpg`;
        await FileSystem.writeAsStringAsync(thumbnailUri, arrayBufferToBase64(thumbnailData), {
          encoding: FileSystem.EncodingType.Base64,
        });

        // Clean up
        await this.ffmpeg.deleteFile(inputFileName);
        await this.ffmpeg.deleteFile(outputFileName);

        return thumbnailUri;
      } catch (error) {
        console.error('Error generating thumbnail:', error);
        throw new Error('Failed to generate thumbnail');
      }
    });
  }

  // Trim video
  public async trimVideo(
    videoUri: string,
    startTime: number,
    endTime: number
  ): Promise<string> {
    return this.queueTask(async () => {
      await this.load();

      if (!this.ffmpeg) {
        throw new Error('FFmpeg not loaded');
      }

      try {
        // Generate unique filenames
        const inputFileName = `input_${Date.now()}_${Math.floor(Math.random() * 10000)}.mp4`;
        const outputFileName = `output_${Date.now()}_${Math.floor(Math.random() * 10000)}.mp4`;

        // Write the input file to FFmpeg's virtual file system
        await this.ffmpeg.writeFile(inputFileName, await fetchFile(videoUri));

        // Run FFmpeg to trim the video
        await this.ffmpeg.exec([
          '-ss', startTime.toString(),
          '-i', inputFileName,
          '-t', (endTime - startTime).toString(),
          '-c', 'copy',
          outputFileName
        ]);

        // Read the output video
        const outputData = await this.ffmpeg.readFile(outputFileName);

        // Save the output to a temporary file
        const tempDir = FileSystem.cacheDirectory + 'videos/';

        const outputUri = tempDir + `trimmed_${Date.now()}_${Math.floor(Math.random() * 10000)}.mp4`;
        await FileSystem.writeAsStringAsync(outputUri, arrayBufferToBase64(outputData), {
          encoding: FileSystem.EncodingType.Base64,
        });

        // Clean up
        await this.ffmpeg.deleteFile(inputFileName);
        await this.ffmpeg.deleteFile(outputFileName);

        return outputUri;
      } catch (error) {
        console.error('Error trimming video:', error);
        throw new Error('Failed to trim video');
      }
    });
  }

  // Apply filter to video
  public async applyFilter(
    videoUri: string,
    filter: string
  ): Promise<string> {
    return this.queueTask(async () => {
      await this.load();

      if (!this.ffmpeg) {
        throw new Error('FFmpeg not loaded');
      }

      try {
        // Generate unique filenames
        const inputFileName = `input_${Date.now()}_${Math.floor(Math.random() * 10000)}.mp4`;
        const outputFileName = `output_${Date.now()}_${Math.floor(Math.random() * 10000)}.mp4`;

        // Write the input file to FFmpeg's virtual file system
        await this.ffmpeg.writeFile(inputFileName, await fetchFile(videoUri));

        // Run FFmpeg to apply the filter
        await this.ffmpeg.exec([
          '-i', inputFileName,
          '-vf', filter,
          '-c:a', 'copy',
          outputFileName
        ]);

        // Read the output video
        const outputData = await this.ffmpeg.readFile(outputFileName);

        // Save the output to a temporary file
        const tempDir = FileSystem.cacheDirectory + 'videos/';

        const outputUri = tempDir + `filtered_${Date.now()}_${Math.floor(Math.random() * 10000)}.mp4`;
        await FileSystem.writeAsStringAsync(outputUri, arrayBufferToBase64(outputData), {
          encoding: FileSystem.EncodingType.Base64,
        });

        // Clean up
        await this.ffmpeg.deleteFile(inputFileName);
        await this.ffmpeg.deleteFile(outputFileName);

        return outputUri;
      } catch (error) {
        console.error('Error applying filter to video:', error);
        throw new Error('Failed to apply filter to video');
      }
    });
  }

  // Export video with options
  public async exportVideo(
    videoUri: string,
    options: ExportOptions = {}
  ): Promise<string> {
    return this.queueTask(async () => {
      await this.load();

      if (!this.ffmpeg) {
        throw new Error('FFmpeg not loaded');
      }

      try {
        // Generate unique filenames
        const inputFileName = `input_${Date.now()}_${Math.floor(Math.random() * 10000)}.mp4`;
        const outputFileName = `output_${Date.now()}_${Math.floor(Math.random() * 10000)}.mp4`;

        // Write the input file to FFmpeg's virtual file system
        await this.ffmpeg.writeFile(inputFileName, await fetchFile(videoUri));

        // Build FFmpeg command
        const ffmpegArgs = ['-i', inputFileName];

        // Add trim options if provided
        if (options.startTime !== undefined && options.endTime !== undefined) {
          ffmpegArgs.push('-ss', options.startTime.toString());
          ffmpegArgs.push('-t', (options.endTime - options.startTime).toString());
        }

        // Add filter if provided
        if (options.filter) {
          ffmpegArgs.push('-vf', options.filter);
        }

        // Add resolution if provided
        if (options.width && options.height) {
          if (!options.filter) {
            ffmpegArgs.push('-vf', `scale=${options.width}:${options.height}`);
          } else {
            // Append scale to existing filter
            const filterIndex = ffmpegArgs.indexOf('-vf') + 1;
            ffmpegArgs[filterIndex] = `${ffmpegArgs[filterIndex]},scale=${options.width}:${options.height}`;
          }
        }

        // Add bitrate if provided
        if (options.bitrate) {
          ffmpegArgs.push('-b:v', options.bitrate);
        }

        // Add fps if provided
        if (options.fps) {
          ffmpegArgs.push('-r', options.fps.toString());
        }

        // Add codec if provided
        if (options.codec) {
          ffmpegArgs.push('-c:v', options.codec);
        } else {
          ffmpegArgs.push('-c:v', 'libx264');
        }

        // Add output file
        ffmpegArgs.push('-preset', 'fast');
        ffmpegArgs.push('-c:a', 'aac');
        ffmpegArgs.push(outputFileName);

        // Run FFmpeg
        await this.ffmpeg.exec(ffmpegArgs);

        // Read the output video
        const outputData = await this.ffmpeg.readFile(outputFileName);

        // Save the output to a temporary file
        const tempDir = FileSystem.cacheDirectory + 'videos/';

        const outputUri = tempDir + `exported_${Date.now()}_${Math.floor(Math.random() * 10000)}.mp4`;
        await FileSystem.writeAsStringAsync(outputUri, arrayBufferToBase64(outputData), {
          encoding: FileSystem.EncodingType.Base64,
        });

        // Clean up
        await this.ffmpeg.deleteFile(inputFileName);
        await this.ffmpeg.deleteFile(outputFileName);

        return outputUri;
      } catch (error) {
        console.error('Error exporting video:', error);
        throw new Error('Failed to export video');
      }
    });
  }

  // Extract audio from video
  public async extractAudio(
    videoUri: string,
    format: 'mp3' | 'aac' | 'wav' = 'mp3'
  ): Promise<string> {
    return this.queueTask(async () => {
      await this.load();

      if (!this.ffmpeg) {
        throw new Error('FFmpeg not loaded');
      }

      try {
        // Generate unique filenames
        const inputFileName = `input_${Date.now()}_${Math.floor(Math.random() * 10000)}.mp4`;
        const outputFileName = `audio_${Date.now()}_${Math.floor(Math.random() * 10000)}.${format}`;

        // Write the input file to FFmpeg's virtual file system
        await this.ffmpeg.writeFile(inputFileName, await fetchFile(videoUri));

        // Run FFmpeg to extract audio
        await this.ffmpeg.exec([
          '-i', inputFileName,
          '-vn', // No video
          '-acodec', format === 'mp3' ? 'libmp3lame' : format === 'aac' ? 'aac' : 'pcm_s16le',
          '-q:a', '2', // Quality
          outputFileName
        ]);

        // Read the output audio
        const outputData = await this.ffmpeg.readFile(outputFileName);

        // Save the output to a temporary file
        const tempDir = FileSystem.cacheDirectory + 'audio/';

        const outputUri = tempDir + `audio_${Date.now()}_${Math.floor(Math.random() * 10000)}.${format}`;
        await FileSystem.writeAsStringAsync(outputUri, arrayBufferToBase64(outputData), {
          encoding: FileSystem.EncodingType.Base64,
        });

        // Clean up
        await this.ffmpeg.deleteFile(inputFileName);
        await this.ffmpeg.deleteFile(outputFileName);

        return outputUri;
      } catch (error) {
        console.error('Error extracting audio:', error);
        throw new Error('Failed to extract audio');
      }
    });
  }
}

// Helper function to convert ArrayBuffer to Base64
function arrayBufferToBase64(buffer: Uint8Array): string {
  let binary = '';
  const bytes = new Uint8Array(buffer);
  const len = bytes.byteLength;

  for (let i = 0; i < len; i++) {
    binary += String.fromCharCode(bytes[i]);
  }

  return btoa(binary);
}
