import * as tf from '@tensorflow/tfjs-react-native';
import * as FileSystem from 'expo-file-system';
import { manipulateAsync, SaveFormat } from 'expo-image-manipulator';
import { FFmpegProcessor } from '../video/FFmpegProcessor';

// Constants for shot detection
const FRAME_INTERVAL = 0.5; // Extract frames every 0.5 seconds
const SIMILARITY_THRESHOLD = 0.7; // Threshold for determining a shot boundary
const MODEL_URL = 'https://tfhub.dev/google/tfjs-model/imagenet/mobilenet_v2_100_224/feature_vector/2/default/1';

// Interface for shot boundary
export interface ShotBoundary {
  startTime: number;
  endTime: number;
  score: number;
}

// Class for shot boundary detection
export class ShotDetectionService {
  private model: tf.LayersModel | null = null;
  private isModelLoaded: boolean = false;
  private ffmpegProcessor: FFmpegProcessor;
  private modelLoadPromise: Promise<void> | null = null;

  constructor() {
    this.ffmpegProcessor = new FFmpegProcessor();
    this.modelLoadPromise = this.loadModel();
  }

  // Load the MobileNet model for feature extraction
  private async loadModel(): Promise<void> {
    if (this.isModelLoaded) {
      return;
    }

    try {
      // Initialize TensorFlow.js if needed
      if (!tf.ready()) {
        await tf.ready();
      }

      // Load the model
      this.model = await tf.loadLayersModel(MODEL_URL);
      this.isModelLoaded = true;
      console.log('Shot detection model loaded successfully');
    } catch (error) {
      console.error('Failed to load shot detection model:', error);
      throw new Error('Failed to load shot detection model');
    }
  }

  // Extract frames from video at regular intervals using FFmpeg
  private async extractFrames(videoUri: string, duration: number): Promise<string[]> {
    console.log('Extracting frames from video...');

    try {
      // Ensure FFmpeg is loaded
      await this.ffmpegProcessor.load();

      const frameCount = Math.floor(duration / FRAME_INTERVAL);
      const frames: string[] = [];

      // Extract frames at regular intervals
      for (let i = 0; i < frameCount; i++) {
        const timePosition = i * FRAME_INTERVAL;

        // Generate thumbnail at specific time position
        const thumbnailUri = await this.ffmpegProcessor.generateThumbnail(videoUri, {
          time: timePosition,
          width: 224,  // MobileNet input size
          height: 224, // MobileNet input size
          quality: 80,
        });

        frames.push(thumbnailUri);
      }

      return frames;
    } catch (error) {
      console.error('Error extracting frames:', error);

      // Fallback to mock implementation if FFmpeg fails
      const frameCount = Math.floor(duration / FRAME_INTERVAL);
      return Array(frameCount).fill(videoUri);
    }
  }

  // Process an image for the model
  private async preprocessImage(imageUri: string): Promise<tf.Tensor3D> {
    // Resize image to 224x224 (MobileNet input size)
    const resizedImage = await manipulateAsync(
      imageUri,
      [{ resize: { width: 224, height: 224 } }],
      { format: SaveFormat.JPEG }
    );

    // Load the image as a tensor
    const imgBuffer = await FileSystem.readAsStringAsync(resizedImage.uri, {
      encoding: FileSystem.EncodingType.Base64,
    });

    // Decode and normalize the image
    const imgTensor = tf.browser.fromPixels(
      { uri: `data:image/jpeg;base64,${imgBuffer}`, width: 224, height: 224 } as any
    );

    // Normalize pixel values to [-1, 1]
    return tf.div(tf.sub(imgTensor, 127.5), 127.5) as tf.Tensor3D;
  }

  // Extract features from an image using MobileNet
  private async extractFeatures(imageUri: string): Promise<tf.Tensor> {
    if (!this.isModelLoaded || !this.model) {
      throw new Error('Model not loaded');
    }

    // Preprocess the image
    const imgTensor = await this.preprocessImage(imageUri);

    // Get features from the model
    const features = this.model.predict(tf.expandDims(imgTensor)) as tf.Tensor;

    // Clean up tensors
    imgTensor.dispose();

    return features;
  }

  // Calculate cosine similarity between two feature vectors
  private cosineSimilarity(a: tf.Tensor, b: tf.Tensor): number {
    // Compute dot product
    const dotProduct = tf.sum(tf.mul(a, b));

    // Compute magnitudes
    const aMagnitude = tf.sqrt(tf.sum(tf.square(a)));
    const bMagnitude = tf.sqrt(tf.sum(tf.square(b)));

    // Compute similarity
    const similarity = tf.div(dotProduct, tf.mul(aMagnitude, bMagnitude));

    // Get the value
    const similarityValue = similarity.dataSync()[0];

    // Clean up tensors
    dotProduct.dispose();
    aMagnitude.dispose();
    bMagnitude.dispose();
    similarity.dispose();

    return similarityValue;
  }

  // Detect shot boundaries in a video
  public async detectShots(videoUri: string, duration: number): Promise<ShotBoundary[]> {
    try {
      // Wait for model to load if it hasn't already
      if (!this.isModelLoaded && this.modelLoadPromise) {
        await this.modelLoadPromise;
      } else if (!this.isModelLoaded) {
        await this.loadModel();
      }

      // Get video metadata to ensure accurate duration
      try {
        const metadata = await this.ffmpegProcessor.getMetadata(videoUri);
        if (metadata.duration > 0) {
          duration = metadata.duration;
        }
      } catch (error) {
        console.warn('Could not get video metadata, using provided duration:', error);
      }

      // Extract frames from the video
      const frames = await this.extractFrames(videoUri, duration);

      if (frames.length < 2) {
        return [];
      }

      // Extract features for each frame in batches to improve performance
      const batchSize = 5; // Process 5 frames at a time
      const features: tf.Tensor[] = [];

      for (let i = 0; i < frames.length; i += batchSize) {
        const batch = frames.slice(i, i + batchSize);
        const batchPromises = batch.map(frame => this.extractFeatures(frame));
        const batchFeatures = await Promise.all(batchPromises);
        features.push(...batchFeatures);

        // Report progress
        console.log(`Processed ${Math.min(i + batchSize, frames.length)}/${frames.length} frames`);
      }

      // Detect shot boundaries by comparing consecutive frames
      const shotBoundaries: ShotBoundary[] = [];
      let currentShotStart = 0;

      for (let i = 1; i < features.length; i++) {
        const similarity = this.cosineSimilarity(features[i - 1], features[i]);

        // If similarity is below threshold, we have a shot boundary
        if (similarity < SIMILARITY_THRESHOLD) {
          const startTime = currentShotStart * FRAME_INTERVAL;
          const endTime = i * FRAME_INTERVAL;

          shotBoundaries.push({
            startTime,
            endTime,
            score: 1 - similarity, // Higher score means more different
          });

          currentShotStart = i;
        }
      }

      // Add the last shot
      if (currentShotStart < features.length - 1 || shotBoundaries.length === 0) {
        shotBoundaries.push({
          startTime: currentShotStart * FRAME_INTERVAL,
          endTime: duration,
          score: 1, // Assign maximum score to the last shot
        });
      }

      // Clean up tensors
      features.forEach(tensor => tensor.dispose());

      // Clean up temporary files
      this.cleanupTempFiles(frames);

      return shotBoundaries;
    } catch (error) {
      console.error('Error detecting shots:', error);
      return this.mockDetectShots(videoUri, duration);
    }
  }

  // Clean up temporary thumbnail files
  private async cleanupTempFiles(files: string[]): Promise<void> {
    try {
      for (const file of files) {
        if (file.startsWith(FileSystem.cacheDirectory || '')) {
          await FileSystem.deleteAsync(file, { idempotent: true }).catch(() => {});
        }
      }
    } catch (error) {
      console.warn('Error cleaning up temp files:', error);
    }
  }

  // Mock implementation for demo purposes
  public async mockDetectShots(videoUri: string, duration: number): Promise<ShotBoundary[]> {
    // Simulate processing time
    await new Promise(resolve => setTimeout(resolve, 2000));

    // Generate random shot boundaries
    const shotCount = Math.floor(Math.random() * 5) + 3; // 3-7 shots
    const shotBoundaries: ShotBoundary[] = [];

    let currentTime = 0;
    for (let i = 0; i < shotCount; i++) {
      const shotDuration = Math.random() * 5 + 2; // 2-7 seconds per shot
      const endTime = Math.min(currentTime + shotDuration, duration);

      shotBoundaries.push({
        startTime: currentTime,
        endTime,
        score: Math.random() * 0.5 + 0.5, // Score between 0.5 and 1.0
      });

      currentTime = endTime;

      if (currentTime >= duration) {
        break;
      }
    }

    return shotBoundaries;
  }
}
