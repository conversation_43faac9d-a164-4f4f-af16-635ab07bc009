import * as tf from '@tensorflow/tfjs-react-native';
import * as FileSystem from 'expo-file-system';
import { manipulateAsync, SaveFormat } from 'expo-image-manipulator';
import { FFmpegProcessor } from '../video/FFmpegProcessor';
import { ShotBoundary } from './ShotDetection';

// Constants for highlight detection
const FACE_DETECTION_MODEL_URL = 'https://tfhub.dev/tensorflow/tfjs-model/blazeface/1/default/1';
const MOTION_THRESHOLD = 0.3;
const AUDIO_THRESHOLD = 0.5;

// Interface for highlight
export interface Highlight {
  startTime: number;
  endTime: number;
  score: number;
  type: 'face' | 'motion' | 'audio' | 'combined';
}

// Class for highlight detection
export class HighlightScoringService {
  private faceModel: tf.GraphModel | null = null;
  private isModelLoaded: boolean = false;
  private ffmpegProcessor: FFmpegProcessor;
  private modelLoadPromise: Promise<void> | null = null;

  constructor() {
    this.ffmpegProcessor = new FFmpegProcessor();
    this.modelLoadPromise = this.loadModel();
  }

  // Load the face detection model
  private async loadModel(): Promise<void> {
    if (this.isModelLoaded) {
      return;
    }

    try {
      // Initialize TensorFlow.js if needed
      if (!tf.ready()) {
        await tf.ready();
      }

      // Load the model
      this.faceModel = await tf.loadGraphModel(FACE_DETECTION_MODEL_URL);
      this.isModelLoaded = true;
      console.log('Face detection model loaded successfully');
    } catch (error) {
      console.error('Failed to load face detection model:', error);
      throw new Error('Failed to load face detection model');
    }
  }

  // Process an image for face detection
  private async preprocessImage(imageUri: string): Promise<tf.Tensor3D> {
    // Resize image to 128x128 (BlazeFace input size)
    const resizedImage = await manipulateAsync(
      imageUri,
      [{ resize: { width: 128, height: 128 } }],
      { format: SaveFormat.JPEG }
    );

    // Load the image as a tensor
    const imgBuffer = await FileSystem.readAsStringAsync(resizedImage.uri, {
      encoding: FileSystem.EncodingType.Base64,
    });

    // Decode the image
    const imgTensor = tf.browser.fromPixels(
      { uri: `data:image/jpeg;base64,${imgBuffer}`, width: 128, height: 128 } as any
    );

    // Normalize pixel values to [0, 1]
    return tf.div(imgTensor, 255) as tf.Tensor3D;
  }

  // Detect faces in an image
  private async detectFaces(imageUri: string): Promise<number> {
    if (!this.isModelLoaded || !this.faceModel) {
      throw new Error('Model not loaded');
    }

    // Preprocess the image
    const imgTensor = await this.preprocessImage(imageUri);

    // Run face detection
    const predictions = await this.faceModel.predict(tf.expandDims(imgTensor)) as tf.Tensor[];

    // Get the number of faces and their confidence scores
    const faces = await this.processFaceDetections(predictions);

    // Clean up tensors
    imgTensor.dispose();
    predictions.forEach(tensor => tensor.dispose());

    // Return a score based on the number and confidence of faces
    return faces.length > 0 ? Math.min(faces.length * 0.3, 1) : 0;
  }

  // Process face detection results
  private async processFaceDetections(predictions: tf.Tensor[]): Promise<Array<{ confidence: number }>> {
    // This is a simplified implementation
    // In a real app, we would extract the actual face detections from the model output

    // For demo purposes, we'll just return a random number of faces
    const faceCount = Math.floor(Math.random() * 3); // 0-2 faces

    return Array(faceCount).fill(0).map(() => ({
      confidence: Math.random() * 0.5 + 0.5, // Confidence between 0.5 and 1.0
    }));
  }

  // Detect motion in a sequence of frames
  // Note: In a real implementation, this would analyze optical flow between frames
  private async detectMotion(frames: string[]): Promise<number[]> {
    // This is a mock implementation
    // In a real app, we would compute optical flow between consecutive frames

    // For demo purposes, we'll just return random motion scores
    return frames.map(() => Math.random());
  }

  // Analyze audio amplitude
  // Note: In a real implementation, this would analyze the audio track
  private async analyzeAudio(videoUri: string, duration: number): Promise<number[]> {
    // This is a mock implementation
    // In a real app, we would extract and analyze the audio track

    // For demo purposes, we'll just return random audio amplitude values
    const sampleCount = Math.ceil(duration);
    return Array(sampleCount).fill(0).map(() => Math.random());
  }

  // Extract frames from video at specific timestamps using FFmpeg
  private async extractFramesAtTimestamps(
    videoUri: string,
    timestamps: number[]
  ): Promise<string[]> {
    try {
      // Ensure FFmpeg is loaded
      await this.ffmpegProcessor.load();

      const frames: string[] = [];

      // Extract frames at specified timestamps
      for (const timestamp of timestamps) {
        // Generate thumbnail at specific time position
        const thumbnailUri = await this.ffmpegProcessor.generateThumbnail(videoUri, {
          time: timestamp,
          width: 128,  // BlazeFace input size
          height: 128, // BlazeFace input size
          quality: 80,
        });

        frames.push(thumbnailUri);
      }

      return frames;
    } catch (error) {
      console.error('Error extracting frames at timestamps:', error);

      // Fallback to mock implementation
      return Array(timestamps.length).fill(videoUri);
    }
  }

  // Score highlights based on faces, motion, and audio
  public async scoreHighlights(
    videoUri: string,
    duration: number,
    shotBoundaries: ShotBoundary[]
  ): Promise<Highlight[]> {
    try {
      // Wait for model to load if it hasn't already
      if (!this.isModelLoaded && this.modelLoadPromise) {
        await this.modelLoadPromise;
      } else if (!this.isModelLoaded) {
        await this.loadModel();
      }

      // Get video metadata to ensure accurate duration
      try {
        const metadata = await this.ffmpegProcessor.getMetadata(videoUri);
        if (metadata.duration > 0) {
          duration = metadata.duration;
        }
      } catch (error) {
        console.warn('Could not get video metadata, using provided duration:', error);
      }

      // Define sampling parameters
      const frameInterval = 1; // 1 frame per second
      const frameCount = Math.ceil(duration / frameInterval);

      // Generate timestamps for frame extraction
      const timestamps: number[] = [];
      for (let i = 0; i < frameCount; i++) {
        timestamps.push(i * frameInterval);
      }

      // Extract frames from the video at regular intervals
      const frames = await this.extractFramesAtTimestamps(videoUri, timestamps);

      // Analyze each aspect in parallel
      const [faceScoresPromise, motionScoresPromise, audioScoresPromise] = await Promise.all([
        // Face detection
        Promise.all(frames.map(frame => this.detectFaces(frame))),

        // Motion detection
        this.detectMotion(frames),

        // Audio analysis
        this.analyzeAudio(videoUri, duration)
      ]);

      const faceScores = await faceScoresPromise;
      const motionScores = await motionScoresPromise;
      const audioScores = await audioScoresPromise;

      // Combine scores to find highlights
      const highlights: Highlight[] = [];

      // Process each shot boundary
      for (const shot of shotBoundaries) {
        const startFrame = Math.floor(shot.startTime / frameInterval);
        const endFrame = Math.min(Math.ceil(shot.endTime / frameInterval), frameCount - 1);

        // Skip invalid shot boundaries
        if (startFrame >= endFrame || startFrame < 0 || endFrame >= frameCount) {
          continue;
        }

        // Get scores for this shot
        const shotFaceScores = faceScores.slice(startFrame, endFrame + 1);
        const shotMotionScores = motionScores.slice(startFrame, endFrame + 1);
        const shotAudioScores = audioScores.slice(
          Math.floor(shot.startTime),
          Math.min(Math.ceil(shot.endTime), audioScores.length)
        );

        // Find face highlights
        if (shotFaceScores.length > 0) {
          const maxFaceScore = Math.max(...shotFaceScores);
          if (maxFaceScore > 0.5) {
            const faceHighlightIndex = shotFaceScores.indexOf(maxFaceScore);
            highlights.push({
              startTime: shot.startTime + faceHighlightIndex * frameInterval,
              endTime: shot.startTime + (faceHighlightIndex + 1) * frameInterval,
              score: maxFaceScore,
              type: 'face',
            });
          }
        }

        // Find motion highlights
        if (shotMotionScores.length > 0) {
          const maxMotionScore = Math.max(...shotMotionScores);
          if (maxMotionScore > MOTION_THRESHOLD) {
            const motionHighlightIndex = shotMotionScores.indexOf(maxMotionScore);
            highlights.push({
              startTime: shot.startTime + motionHighlightIndex * frameInterval,
              endTime: shot.startTime + (motionHighlightIndex + 1) * frameInterval,
              score: maxMotionScore,
              type: 'motion',
            });
          }
        }

        // Find audio highlights
        if (shotAudioScores.length > 0) {
          const maxAudioScore = Math.max(...shotAudioScores);
          if (maxAudioScore > AUDIO_THRESHOLD) {
            const audioHighlightIndex = shotAudioScores.indexOf(maxAudioScore);
            highlights.push({
              startTime: shot.startTime + audioHighlightIndex,
              endTime: shot.startTime + audioHighlightIndex + 1,
              score: maxAudioScore,
              type: 'audio',
            });
          }
        }

        // Add a combined highlight for the entire shot
        const combinedScore = (
          (shotFaceScores.length > 0 ? Math.max(...shotFaceScores) : 0) * 0.4 +
          (shotMotionScores.length > 0 ? Math.max(...shotMotionScores) : 0) * 0.3 +
          (shotAudioScores.length > 0 ? Math.max(...shotAudioScores) : 0) * 0.3
        );

        highlights.push({
          startTime: shot.startTime,
          endTime: shot.endTime,
          score: combinedScore,
          type: 'combined',
        });
      }

      // Sort highlights by score (highest first)
      highlights.sort((a, b) => b.score - a.score);

      // Clean up temporary files
      this.cleanupTempFiles(frames);

      return highlights;
    } catch (error) {
      console.error('Error scoring highlights:', error);
      return this.mockScoreHighlights(videoUri, duration, shotBoundaries);
    }
  }

  // Clean up temporary thumbnail files
  private async cleanupTempFiles(files: string[]): Promise<void> {
    try {
      for (const file of files) {
        if (file.startsWith(FileSystem.cacheDirectory || '')) {
          await FileSystem.deleteAsync(file, { idempotent: true }).catch(() => {});
        }
      }
    } catch (error) {
      console.warn('Error cleaning up temp files:', error);
    }
  }

  // Mock implementation for demo purposes
  public async mockScoreHighlights(
    videoUri: string,
    duration: number,
    shotBoundaries: ShotBoundary[]
  ): Promise<Highlight[]> {
    // Simulate processing time
    await new Promise(resolve => setTimeout(resolve, 2000));

    const highlights: Highlight[] = [];

    // Generate highlights for each shot
    for (const shot of shotBoundaries) {
      const shotDuration = shot.endTime - shot.startTime;

      // Skip very short shots
      if (shotDuration < 1) continue;

      // Add a face highlight
      if (Math.random() > 0.3) {
        const startOffset = Math.random() * (shotDuration - 1);
        highlights.push({
          startTime: shot.startTime + startOffset,
          endTime: shot.startTime + startOffset + 1,
          score: Math.random() * 0.5 + 0.5,
          type: 'face',
        });
      }

      // Add a motion highlight
      if (Math.random() > 0.4) {
        const startOffset = Math.random() * (shotDuration - 1);
        highlights.push({
          startTime: shot.startTime + startOffset,
          endTime: shot.startTime + startOffset + 1,
          score: Math.random() * 0.5 + 0.5,
          type: 'motion',
        });
      }

      // Add an audio highlight
      if (Math.random() > 0.5) {
        const startOffset = Math.random() * (shotDuration - 1);
        highlights.push({
          startTime: shot.startTime + startOffset,
          endTime: shot.startTime + startOffset + 1,
          score: Math.random() * 0.5 + 0.5,
          type: 'audio',
        });
      }

      // Add a combined highlight for the entire shot
      highlights.push({
        startTime: shot.startTime,
        endTime: shot.endTime,
        score: Math.random() * 0.5 + 0.5,
        type: 'combined',
      });
    }

    // Sort highlights by score (highest first)
    highlights.sort((a, b) => b.score - a.score);

    return highlights;
  }
}
