/**
 * Enhanced color system for the AI Video Editor app
 * Includes modern gradients, video-specific colors, and improved accessibility
 */

// Primary brand colors
const primaryLight = '#6366F1'; // Indigo
const primaryDark = '#8B5CF6';  // Purple

// Video editor specific colors
const videoColors = {
  record: '#EF4444',     // Red for recording
  play: '#10B981',       // Green for play
  pause: '#F59E0B',      // Amber for pause
  trim: '#3B82F6',       // Blue for trim
  filter: '#8B5CF6',     // Purple for filters
  text: '#EC4899',       // Pink for text overlays
  export: '#059669',     // Emerald for export
};

// Gradient definitions
export const Gradients = {
  light: {
    primary: ['#6366F1', '#8B5CF6'],
    secondary: ['#10B981', '#059669'],
    accent: ['#F59E0B', '#EF4444'],
    surface: ['#F8FAFC', '#F1F5F9'],
    video: ['#1E293B', '#0F172A'],
  },
  dark: {
    primary: ['#8B5CF6', '#A855F7'],
    secondary: ['#10B981', '#059669'],
    accent: ['#F59E0B', '#EF4444'],
    surface: ['#1E293B', '#0F172A'],
    video: ['#0F172A', '#020617'],
  },
};

export const Colors = {
  light: {
    // Base colors
    text: '#0F172A',
    textSecondary: '#475569',
    textMuted: '#64748B',
    background: '#FFFFFF',
    backgroundSecondary: '#F8FAFC',

    // Interactive colors
    tint: primaryLight,
    primary: primaryLight,
    secondary: '#10B981',
    accent: '#F59E0B',

    // UI elements
    icon: '#64748B',
    iconActive: primaryLight,
    tabIconDefault: '#94A3B8',
    tabIconSelected: primaryLight,

    // Surfaces
    cardBackground: '#FFFFFF',
    surfaceElevated: '#F8FAFC',
    border: '#E2E8F0',
    borderLight: '#F1F5F9',

    // Video editor colors
    videoEditorPrimary: videoColors.record,
    videoEditorSecondary: videoColors.play,
    videoEditorSuccess: videoColors.export,
    videoEditorAccent: videoColors.filter,

    // Semantic colors
    success: '#10B981',
    warning: '#F59E0B',
    error: '#EF4444',
    info: '#3B82F6',

    // Video specific
    recordButton: videoColors.record,
    playButton: videoColors.play,
    trimColor: videoColors.trim,
    filterColor: videoColors.filter,
    textOverlayColor: videoColors.text,
    exportColor: videoColors.export,

    // Overlay colors
    overlay: 'rgba(0, 0, 0, 0.5)',
    overlayLight: 'rgba(0, 0, 0, 0.3)',
    overlayHeavy: 'rgba(0, 0, 0, 0.7)',
  },
  dark: {
    // Base colors
    text: '#F8FAFC',
    textSecondary: '#CBD5E1',
    textMuted: '#94A3B8',
    background: '#0F172A',
    backgroundSecondary: '#1E293B',

    // Interactive colors
    tint: primaryDark,
    primary: primaryDark,
    secondary: '#10B981',
    accent: '#F59E0B',

    // UI elements
    icon: '#94A3B8',
    iconActive: primaryDark,
    tabIconDefault: '#64748B',
    tabIconSelected: primaryDark,

    // Surfaces
    cardBackground: '#1E293B',
    surfaceElevated: '#334155',
    border: '#334155',
    borderLight: '#475569',

    // Video editor colors
    videoEditorPrimary: videoColors.record,
    videoEditorSecondary: videoColors.play,
    videoEditorSuccess: videoColors.export,
    videoEditorAccent: videoColors.filter,

    // Semantic colors
    success: '#10B981',
    warning: '#F59E0B',
    error: '#EF4444',
    info: '#3B82F6',

    // Video specific
    recordButton: videoColors.record,
    playButton: videoColors.play,
    trimColor: videoColors.trim,
    filterColor: videoColors.filter,
    textOverlayColor: videoColors.text,
    exportColor: videoColors.export,

    // Overlay colors
    overlay: 'rgba(0, 0, 0, 0.6)',
    overlayLight: 'rgba(0, 0, 0, 0.4)',
    overlayHeavy: 'rgba(0, 0, 0, 0.8)',
  },
};

// Shadow definitions for elevation
export const Shadows = {
  small: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  medium: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.15,
    shadowRadius: 4,
    elevation: 4,
  },
  large: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.2,
    shadowRadius: 8,
    elevation: 8,
  },
};
